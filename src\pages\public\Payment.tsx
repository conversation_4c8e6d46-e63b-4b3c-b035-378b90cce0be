import { useState, useEffect } from 'react';
import { ShieldCheckIcon, StarIcon, CreditCardIcon, BanknotesIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import PaymentSuccessPage from './PaymentSuccessPage'; // Import the new component

const Payment = () => {
  type Plan = typeof plans[number];
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [couponCode, setCouponCode] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  // Removed accessCode and copySuccess from here as they are now in PaymentSuccessPage
  // Removed useEffect for accessCode from here

  const plans = [
    {
      id: 'monthly',
      name: 'Monthly Plan',
      price: 19,
      billing: 'month',
      features: [
        'Access to all exams',
        'Live proctoring sessions',
        'Email support (24h response)',
        'Basic analytics dashboard',
        '5 exam attempts per month'
      ],
      isPopular: false,
      savings: null
    },
    {
      id: 'annual',
      name: 'Annual Plan',
      price: 190,
      billing: 'year',
      features: [
        'Everything in Monthly',
        'Priority support (4h response)',
        'Advanced analytics',
        'Unlimited exam attempts',
        'Early access to new features',
        'Free practice exams'
      ],
      isPopular: true,
      savings: 38
    },
  ];

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: CreditCardIcon },
    { id: 'paypal', name: 'PayPal', icon: BanknotesIcon },
    { id: 'bank', name: 'Bank Transfer', icon: ArrowPathIcon }
  ];

  const processPayment = () => {
    setIsProcessing(true);
    setTimeout(() => {
      setIsProcessing(false);
      setPaymentSuccess(true);
      // Access code generation is now handled by PaymentSuccessPage
    }, 2000);
  };

  const calculateFinalPrice = () => {
    if (!selectedPlan) return 0;
    let price = selectedPlan.price;
    
    if (couponCode === 'SAVE20') {
      price = price * 0.8;
    } else if (couponCode === 'SAVE10') {
      price = price * 0.9;
    }
    
    return price.toFixed(2);
  };

  // Conditional rendering for the success page
  if (paymentSuccess && selectedPlan) {
    return (
      <PaymentSuccessPage 
        selectedPlan={selectedPlan} 
        finalPrice={String(calculateFinalPrice())} 
      />
    );
  }

  return (
    <div className="payment-container">
      <div className="payment-content">
        <div className="payment-header">
          <h1>Choose Your Plan</h1>
          <p>Select the subscription that fits your needs. Enjoy advanced features and flexibility.</p>
        </div>

        <div className="plans-grid">
          {plans.map((plan) => (
            <div 
              key={plan.id}
              className={`plan-card ${plan.isPopular ? 'popular' : ''} ${selectedPlan?.id === plan.id ? 'selected' : ''}`}
              onClick={() => setSelectedPlan(plan)}
            >
              {plan.isPopular && (
                <div className="popular-badge">
                  <StarIcon className="icon" />
                  Most Popular
                </div>
              )}
              <h2>{plan.name}</h2>
              <div className="price">
                ${plan.price}
                <span>/{plan.billing}</span>
              </div>
              {plan.savings && (
                <div className="savings-badge">
                  Save ${plan.savings}
                </div>
              )}
              <ul className="features">
                {plan.features.map((feature, index) => (
                  <li key={index} className="feature-item">
                    {/* CheckCircleIcon is not imported in Payment.jsx, assuming it's available globally or via Tailwind */}
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="icon w-6 h-6">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {feature}
                  </li>
                ))}
              </ul>
              <div className="select-button">
                {selectedPlan?.id === plan.id ? 'Selected Plan' : 'Select Plan'}
              </div>
            </div>
          ))}
        </div>

        {selectedPlan && (
          <div className="payment-details fade-in">
            <div className="coupon-section">
              <h2>Apply Coupon Code</h2>
              <div className="coupon-input">
                <input
                  type="text"
                  placeholder="Enter coupon code"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                />
                <button className="apply-button">Apply</button>
              </div>
              <div className="coupon-tags">
                <span onClick={() => setCouponCode('SAVE20')}>SAVE20</span>
                <span onClick={() => setCouponCode('SAVE10')}>SAVE10</span>
              </div>
            </div>

            <div className="payment-methods">
              <h2>Payment Method</h2>
              <div className="method-options">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className={`method-option ${paymentMethod === method.id ? 'selected' : ''}`}
                    onClick={() => setPaymentMethod(method.id)}
                  >
                    <method.icon className="icon" />
                    <span>{method.name}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="order-summary">
              <h2>Order Summary</h2>
              <div className="summary-row">
                <span>Plan:</span>
                <span>{selectedPlan.name}</span>
              </div>
              <div className="summary-row">
                <span>Billing:</span>
                <span>{selectedPlan.billing === 'month' ? 'Monthly' : 'Annual'}</span>
              </div>
              {couponCode && (
                <div className="summary-row">
                  <span>Discount:</span>
                  <span className="discount">
                    {couponCode === 'SAVE20' ? '20%' : '10%'} off
                  </span>
                </div>
              )}
              <div className="summary-row total">
                <span>Total:</span>
                <span>${calculateFinalPrice()}</span>
              </div>
            </div>

            <div className="security-info">
              <ShieldCheckIcon className="icon" />
              <span>Your payment is **secure and encrypted**</span>
            </div>

            <button
              className={`pay-button ${isProcessing ? 'processing' : ''}`}
              onClick={processPayment}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <>
                  <ArrowPathIcon className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" />
                  Processing...
                </>
              ) : (
                `Pay $${calculateFinalPrice()} Now`
              )}
            </button>
          </div>
        )}
      </div>

      <style>{`
        :root {
            --primary-color: #4f46e5;
            --primary-dark: #4338ca;
            --primary-light: #eef2ff;
            --green-success: #10b981;
            --green-light: #ecfdf5;
            --text-dark: #1f2937;
            --text-medium: #4b5563;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            --code-bg: #f3f4f6;
            --code-text: #374151;
        }

        .payment-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          padding: 3rem 2rem;
          display: flex;
          justify-content: center;
          align-items: flex-start;
          font-family: 'Inter', sans-serif;
        }

        .payment-content {
          max-width: 900px;
          width: 100%;
          margin: 0 auto;
          background: #ffffff;
          border-radius: 1rem;
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
          padding: 3rem;
          animation: fadeIn 0.8s ease-out;
        }

        .payment-header {
          text-align: center;
          margin-bottom: 3.5rem;
        }

        .payment-header h1 {
          font-size: 2.5rem;
          font-weight: 800;
          color: var(--text-dark);
          margin-bottom: 0.75rem;
          letter-spacing: -0.025em;
        }

        .payment-header p {
          font-size: 1.1rem;
          color: var(--text-light);
          max-width: 600px;
          margin: 0 auto;
          line-height: 1.6;
        }

        .plans-grid {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 2rem;
          margin-bottom: 3rem;
          transition: all 0.4s ease-in-out;
        }

        .plan-card {
          background: white;
          border-radius: 1rem;
          padding: 2.5rem;
          border: 2px solid var(--border-color);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
          cursor: pointer;
          transition: all 0.3s ease-in-out, transform 0.2s ease-out;
          position: relative;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .plan-card:hover {
          transform: translateY(-8px) scale(1.01);
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
          border-color: var(--primary-color);
        }

        .plan-card.selected {
          border-color: var(--primary-color);
          background: var(--primary-light);
          box-shadow: 0 10px 20px rgba(79, 70, 229, 0.15);
          transform: scale(1.02);
        }

        .plan-card.popular {
          border: 2px solid var(--green-success);
        }

        .popular-badge {
          position: absolute;
          top: -1rem;
          right: 1.5rem;
          background: var(--green-success);
          color: white;
          padding: 0.4rem 1.2rem;
          border-radius: 9999px;
          font-size: 0.8rem;
          font-weight: 700;
          display: flex;
          align-items: center;
          gap: 0.3rem;
          box-shadow: 0 4px 10px rgba(16, 185, 129, 0.3);
          transform: translateY(-50%);
          transition: all 0.3s ease-out;
        }

        .popular-badge .icon {
          width: 1.1rem;
          height: 1.1rem;
        }

        .plan-card h2 {
          font-size: 1.6rem;
          font-weight: 700;
          color: var(--text-dark);
          margin-bottom: 1rem;
        }

        .price {
          font-size: 3.2rem;
          font-weight: 800;
          color: var(--primary-color);
          margin-bottom: 0.75rem;
          display: flex;
          align-items: baseline;
        }

        .price span {
          font-size: 1.1rem;
          font-weight: 500;
          color: var(--text-light);
          margin-left: 0.5rem;
        }

        .savings-badge {
          display: inline-block;
          background: var(--green-light);
          color: var(--green-success);
          padding: 0.3rem 1rem;
          border-radius: 9999px;
          font-size: 0.9rem;
          font-weight: 600;
          margin-bottom: 1.8rem;
          border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .features {
          margin: 1.5rem 0 2.5rem;
          padding: 0;
          list-style: none;
          flex-grow: 1;
        }

        .features .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 0.8rem;
          font-size: 1rem;
          color: var(--text-medium);
          line-height: 1.4;
          transition: all 0.2s ease-in-out;
        }
        
        .features .feature-item:last-child {
            margin-bottom: 0;
        }

        .features .icon {
          width: 1.3rem;
          height: 1.3rem;
          color: var(--green-success);
          margin-right: 0.7rem;
          flex-shrink: 0;
        }

        .select-button {
          width: 100%;
          padding: 1rem;
          background: var(--border-color);
          color: var(--text-medium);
          border: none;
          border-radius: 0.6rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease-in-out;
          font-size: 1.05rem;
          text-align: center;
        }

        .plan-card.selected .select-button {
          background: var(--primary-color);
          color: white;
          box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
        }

        .plan-card:hover .select-button {
          background: #d1d5db;
        }

        .plan-card.selected:hover .select-button {
          background: var(--primary-dark);
        }

        .payment-details {
          background: var(--bg-light);
          border-radius: 1rem;
          padding: 2.5rem;
          box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.03);
          border: 1px solid var(--border-color);
        }

        .payment-details h2 {
          font-size: 1.3rem;
          font-weight: 700;
          color: var(--text-dark);
          margin-bottom: 1.5rem;
          border-bottom: 1px dashed var(--border-color);
          padding-bottom: 0.75rem;
        }

        .coupon-section {
          margin-bottom: 2.5rem;
        }

        .coupon-input {
          display: flex;
          margin-bottom: 0.75rem;
        }

        .coupon-input input {
          flex: 1;
          padding: 0.9rem 1.2rem;
          border: 1px solid var(--border-color);
          border-radius: 0.6rem 0 0 0.6rem;
          font-size: 0.95rem;
          color: var(--text-medium);
          transition: all 0.2s ease-in-out;
        }

        .coupon-input input:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .apply-button {
          padding: 0 1.8rem;
          background: var(--primary-color);
          color: white;
          border: none;
          border-radius: 0 0.6rem 0.6rem 0;
          font-weight: 600;
          cursor: pointer;
          transition: background 0.2s ease-in-out;
          font-size: 0.95rem;
        }

        .apply-button:hover {
          background: var(--primary-dark);
        }

        .coupon-tags {
          display: flex;
          gap: 0.75rem;
          flex-wrap: wrap;
        }

        .coupon-tags span {
          font-size: 0.85rem;
          color: var(--primary-color);
          background: var(--primary-light);
          padding: 0.4rem 1rem;
          border-radius: 9999px;
          cursor: pointer;
          transition: all 0.2s ease-in-out;
          border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .coupon-tags span:hover {
          background: #dce1ff;
          transform: translateY(-2px);
        }

        .payment-methods {
          margin-bottom: 2.5rem;
        }

        .method-options {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
          gap: 1rem;
        }

        .method-option {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: 1.5rem 1rem;
          border: 2px solid var(--border-color);
          border-radius: 0.75rem;
          cursor: pointer;
          transition: all 0.25s ease-in-out;
          font-size: 0.95rem;
          color: var(--text-medium);
          background: white;
        }

        .method-option:hover {
          border-color: var(--primary-color);
          box-shadow: 0 5px 15px rgba(79, 70, 229, 0.1);
        }

        .method-option.selected {
          border-color: var(--primary-color);
          background: var(--primary-light);
          color: var(--primary-dark);
          box-shadow: 0 5px 15px rgba(79, 70, 229, 0.2);
        }

        .method-option .icon {
          width: 2.2rem;
          height: 2.2rem;
          color: var(--primary-color);
          margin-bottom: 0.8rem;
          transition: color 0.25s ease-in-out;
        }

        .method-option.selected .icon {
          color: var(--primary-dark);
        }

        .order-summary {
          background: white;
          border-radius: 0.75rem;
          padding: 1.8rem;
          margin-bottom: 2.5rem;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
          border: 1px solid var(--border-color);
        }

        .order-summary h2 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin-bottom: 1.2rem;
          border-bottom: 1px dashed var(--border-color);
          padding-bottom: 0.75rem;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.8rem;
          font-size: 1rem;
          color: var(--text-medium);
        }

        .summary-row.total {
          font-weight: 700;
          color: var(--text-dark);
          margin-top: 1.5rem;
          padding-top: 1.5rem;
          border-top: 2px solid var(--primary-light);
          font-size: 1.25rem;
        }

        .discount {
          color: var(--green-success);
          font-weight: 700;
        }

        .security-info {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.6rem;
          font-size: 0.9rem;
          color: var(--text-light);
          margin-bottom: 2rem;
          font-weight: 500;
        }

        .security-info .icon {
          width: 1.4rem;
          height: 1.4rem;
          color: var(--green-success);
        }

        .pay-button {
          width: 100%;
          padding: 1.1rem;
          background: var(--primary-color);
          color: white;
          border: none;
          border-radius: 0.6rem;
          font-size: 1.15rem;
          font-weight: 700;
          cursor: pointer;
          transition: all 0.3s ease-in-out;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.75rem;
          box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
        }

        .pay-button:hover:not(.processing) {
          background: var(--primary-dark);
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
          transform: translateY(-2px);
        }

        .pay-button.processing {
          background: #818cf8;
          cursor: not-allowed;
          animation: pulse 1.5s infinite;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0.4); }
            70% { box-shadow: 0 0 0 20px rgba(79, 70, 229, 0); }
            100% { box-shadow: 0 0 0 0 rgba(79, 70, 229, 0); }
        }

        @keyframes slideInUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
          .payment-content {
            padding: 2rem;
          }

          .payment-header h1 {
            font-size: 2rem;
          }

          .payment-header p {
            font-size: 0.95rem;
          }

          .plans-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
          }

          .plan-card {
            padding: 2rem;
          }

          .price {
            font-size: 2.8rem;
          }

          .payment-details {
            padding: 2rem;
          }

          .coupon-input {
            flex-direction: column;
            gap: 0.75rem;
          }

          .coupon-input input {
            border-radius: 0.6rem;
          }

          .apply-button {
            border-radius: 0.6rem;
            width: 100%;
            padding: 0.8rem;
          }

          .method-options {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Payment;
