/* App.css - Now using Tailwind CSS utilities */

/* Custom animations for logo */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Custom drop-shadow filters that aren't available in Tailwind by default */
.logo-hover-blue {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo-hover-react {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

/* Animation class for logo */
@media (prefers-reduced-motion: no-preference) {
  .logo-spin {
    animation: logo-spin infinite 20s linear;
  }
}
