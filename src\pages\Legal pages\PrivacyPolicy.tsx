import { useNavigate } from 'react-router-dom';

const PrivacyPolicy = () => {
  const navigate = useNavigate();

  return (
    <div className="privacy-policy-container">
      <div className="privacy-policy-card">
        <div className="privacy-policy-header">
          <h1>Exam Portal Privacy Policy</h1>
          <p>Last Updated: {new Date().toLocaleDateString()}</p>
        </div>
        
        <div className="privacy-policy-content">
          <section className="policy-section">
            <h2>1. Introduction</h2>
            <p>
              Welcome to Exam Portal ("we," "our," or "us"). We are committed to protecting your 
              personal information and your right to privacy. If you have any questions or concerns 
              about this privacy policy, or our practices with regards to your personal information, 
              please contact <NAME_EMAIL>.
            </p>
          </section>

          <section className="policy-section">
            <h2>2. Information We Collect</h2>
            <p>We collect personal information that you voluntarily provide to us when you:</p>
            <ul>
              <li>Register on the Exam Portal</li>
              <li>Participate in exams or assessments</li>
              <li>Contact us with inquiries</li>
              <li>Subscribe to our communications</li>
            </ul>
            <p>The personal information we collect may include:</p>
            <ul>
              <li>Name and contact details (email address)</li>
              <li>Academic information and exam results</li>
              <li>Technical data (IP address, browser type, device information)</li>
              <li>Usage data (exam attempts, time spent, answers)</li>
            </ul>
          </section>

          <section className="policy-section">
            <h2>3. How We Use Your Information</h2>
            <p>We use the information we collect in various ways, including to:</p>
            <ul>
              <li>Provide, operate, and maintain our exam platform</li>
              <li>Authenticate users and prevent fraud</li>
              <li>Evaluate exam performance and generate results</li>
              <li>Improve, personalize, and expand our services</li>
              <li>Communicate with you, including for customer service</li>
              <li>Comply with legal obligations</li>
            </ul>
          </section>

          <section className="policy-section">
            <h2>4. Data Security</h2>
            <p>
              We implement appropriate technical and organizational measures to protect the security 
              of your personal information. However, please remember that no method of transmission 
              over the Internet, or method of electronic storage is 100% secure.
            </p>
            <p>
              Exam data is encrypted both in transit and at rest. We regularly audit our systems for 
              vulnerabilities and maintain strict access controls.
            </p>
          </section>

          <section className="policy-section">
            <h2>5. Data Retention</h2>
            <p>
              We retain personal information only for as long as necessary to fulfill the purposes 
              outlined in this policy, unless a longer retention period is required or permitted by law.
            </p>
            <p>
              Exam results are typically retained for 5 years to allow for academic appeals and 
              verification. You may request deletion of your account data at any time, subject to 
              certain legal obligations we may have to retain information.
            </p>
          </section>

          <section className="policy-section">
            <h2>6. Your Privacy Rights</h2>
            <p>Depending on your location, you may have the right to:</p>
            <ul>
              <li>Access and receive a copy of your personal data</li>
              <li>Request correction of inaccurate information</li>
              <li>Request deletion of your personal data</li>
              <li>Object to processing of your personal data</li>
              <li>Request restriction of processing</li>
              <li>Withdraw consent at any time</li>
            </ul>
            <p>
              To exercise these rights, please contact <NAME_EMAIL>. We may need to 
              verify your identity before responding to such requests.
            </p>
          </section>

          <section className="policy-section">
            <h2>7. Changes to This Policy</h2>
            <p>
              We may update this Privacy Policy from time to time. We will notify you of any changes 
              by posting the new Privacy Policy on this page and updating the "Last Updated" date.
            </p>
            <p>
              You are advised to review this Privacy Policy periodically for any changes. Changes to 
              this Privacy Policy are effective when they are posted on this page.
            </p>
          </section>

          <section className="policy-section">
            <h2>8. Contact Us</h2>
            <p>
              If you have any questions about this Privacy Policy, please contact us:
            </p>
            <ul>
              <li>By email: <EMAIL></li>
              <li>By mail: 123 Education Street, Tech City, TC 10101</li>
              <li>Through our website contact form</li>
            </ul>
          </section>

          <div className="back-button-container">
            <button 
              className="back-button"
              onClick={() => navigate(-1)}
            >
              Back to Previous Page
            </button>
          </div>
        </div>
      </div>

      <style>{`
        .privacy-policy-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem 1rem;
        }

        .privacy-policy-card {
          width: 100%;
          max-width: 800px;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .privacy-policy-header {
          background: #4f46e5;
          padding: 1.5rem 2rem;
          text-align: center;
          color: white;
        }

        .privacy-policy-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          margin: 0;
        }

        .privacy-policy-header p {
          color: #a5b4fc;
          margin-top: 0.5rem;
          font-size: 0.875rem;
        }

        .privacy-policy-content {
          padding: 2rem;
          color: #374151;
        }

        .policy-section {
          margin-bottom: 2rem;
        }

        .policy-section h2 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 0.5rem;
        }

        .policy-section p {
          margin-bottom: 1rem;
          line-height: 1.6;
        }

        .policy-section ul {
          margin: 1rem 0;
          padding-left: 1.5rem;
        }

        .policy-section li {
          margin-bottom: 0.5rem;
          line-height: 1.6;
        }

        .back-button-container {
          display: flex;
          justify-content: center;
          margin-top: 2rem;
        }

        .back-button {
          padding: 0.75rem 1.5rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: background 0.2s;
        }

        .back-button:hover {
          background: #4338ca;
        }

        @media (max-width: 768px) {
          .privacy-policy-card {
            border-radius: 0;
          }
          
          .privacy-policy-content {
            padding: 1.5rem;
          }
        }
      `}</style>
    </div>
  );
};

export default PrivacyPolicy;