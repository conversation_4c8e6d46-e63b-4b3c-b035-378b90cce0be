import { useParams } from 'react-router-dom';
import { CheckCircleIcon, ClockIcon, FileTextIcon, ChartBarIcon, UserIcon, DownloadIcon } from 'lucide-react';

const Submitted = () => {
  const { examId } = useParams();
  
  // Mock exam results data
  const examData = {
    'js101': {
      title: 'JavaScript Fundamentals Certification',
      date: '2023-06-15T14:30:00Z',
      duration: 90,
      questionsAttempted: 28,
      totalQuestions: 30,
      score: 82,
      passingScore: 70,
      status: 'Passed',
      breakdown: [
        { category: 'Variables & Data Types', correct: 5, total: 5 },
        { category: 'Functions', correct: 7, total: 8 },
        { category: 'DOM Manipulation', correct: 6, total: 7 },
        { category: 'Async Programming', correct: 5, total: 6 },
        { category: 'ES6 Features', correct: 5, total: 4 }
      ]
    },
    'py202': {
      title: 'Python Advanced Concepts',
      date: '2023-06-10T09:00:00Z',
      duration: 120,
      questionsAttempted: 42,
      totalQuestions: 45,
      score: 78,
      passingScore: 75,
      status: 'Passed',
      breakdown: [
        { category: 'OOP Concepts', correct: 8, total: 10 },
        { category: 'Decorators', correct: 6, total: 7 },
        { category: 'Generators', correct: 5, total: 6 },
        { category: 'Async/Await', correct: 7, total: 8 },
        { category: 'Advanced Libraries', correct: 6, total: 7 },
        { category: 'Performance', correct: 5, total: 7 }
      ]
    }
  };

  const exam = examData[examId as keyof typeof examData] || examData['js101'];
  const passed = exam.score >= exam.passingScore;

interface PerformanceSection {
    category: string;
    correct: number;
    total: number;
}

interface ExamData {
    title: string;
    date: string;
    duration: number;
    questionsAttempted: number;
    totalQuestions: number;
    score: number;
    passingScore: number;
    status: string;
    breakdown: PerformanceSection[];
}

const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

  const downloadCertificate = () => {
    // In a real app, this would generate/download a PDF certificate
    alert('Downloading certificate...');
  };

  const downloadResults = () => {
    // In a real app, this would generate/download detailed results
    alert('Downloading detailed results...');
  };

  return (
    <div className="submission-container">
      <div className="submission-content">
        <div className="confirmation-section">
          <div className="success-icon">
            <CheckCircleIcon className="icon" />
          </div>
          <h1>Submission Complete</h1>
          <p className="exam-title">{exam.title}</p>
          <div className={`result-status ${passed ? 'passed' : 'failed'}`}>
            {exam.status} with {exam.score}%
          </div>
        </div>

        <div className="summary-grid">
          <div className="summary-card">
            <h2>
              <ClockIcon className="icon" />
              Exam Details
            </h2>
            <ul>
              <li><strong>Date Taken:</strong> {formatDate(exam.date)}</li>
              <li><strong>Duration:</strong> {exam.duration} minutes</li>
              <li><strong>Questions Attempted:</strong> {exam.questionsAttempted}/{exam.totalQuestions}</li>
              <li><strong>Passing Score:</strong> {exam.passingScore}%</li>
            </ul>
          </div>

          <div className="performance-card">
            <h2>
              <ChartBarIcon className="icon" />
              Performance Breakdown
            </h2>
            <div className="performance-bars">
              {exam.breakdown.map((section: PerformanceSection, index: number) => (
                <div key={index} className="performance-row">
                  <div className="category">{section.category}</div>
                  <div className="bar-container">
                    <div 
                      className="bar"
                      style={{ width: `${(section.correct / section.total) * 100}%` }}
                    ></div>
                  </div>
                  <div className="score">{section.correct}/{section.total}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="actions-card">
            <h2>
              <FileTextIcon className="icon" />
              Next Steps
            </h2>
            <div className="action-buttons">
              <button className="view-results" onClick={() => window.location.href = '/student/results'}>
                View Detailed Results
              </button>
              {passed && (
                <button className="download-certificate" onClick={downloadCertificate}>
                  <DownloadIcon className="icon" />
                  Download Certificate
                </button>
              )}
              <button className="download-results" onClick={downloadResults}>
                <DownloadIcon className="icon" />
                Download Full Report
              </button>
            </div>
          </div>
        </div>

        <div className="proctoring-note">
          <UserIcon className="icon" />
          <p>
            <strong>Proctoring Note:</strong> Your exam session was monitored and recorded. 
            Any suspicious activity will be reviewed by our team.
          </p>
        </div>
      </div>

      <style>{`
        .submission-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .submission-content {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          padding: 2rem;
        }

        .confirmation-section {
          text-align: center;
          margin-bottom: 2rem;
          padding-bottom: 1.5rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .success-icon {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          width: 4rem;
          height: 4rem;
          background: #dcfce7;
          border-radius: 50%;
          margin: 0 auto 1rem;
        }

        .success-icon .icon {
          width: 2.5rem;
          height: 2.5rem;
          color: #16a34a;
        }

        .confirmation-section h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0 0 0.5rem 0;
        }

        .exam-title {
          font-size: 1.125rem;
          color: #4b5563;
          margin: 0 0 1rem 0;
        }

        .result-status {
          display: inline-block;
          padding: 0.5rem 1.5rem;
          border-radius: 9999px;
          font-weight: 600;
          margin-top: 0.5rem;
        }

        .result-status.passed {
          background: #dcfce7;
          color: #166534;
        }

        .result-status.failed {
          background: #fee2e2;
          color: #b91c1c;
        }

        .summary-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
          .summary-grid {
            grid-template-columns: 1fr 1fr;
          }
        }

        .summary-card,
        .performance-card,
        .actions-card {
          background: #f9fafb;
          border-radius: 0.5rem;
          padding: 1.5rem;
          border: 1px solid #e5e7eb;
        }

        .summary-card h2,
        .performance-card h2,
        .actions-card h2 {
          display: flex;
          align-items: center;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .summary-card .icon,
        .performance-card .icon,
        .actions-card .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          color: #4f46e5;
        }

        .summary-card ul {
          margin: 0;
          padding: 0;
          list-style: none;
        }

        .summary-card li {
          margin-bottom: 0.75rem;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .summary-card li strong {
          color: #1f2937;
          font-weight: 500;
          display: inline-block;
          min-width: 120px;
        }

        .performance-bars {
          margin-top: 1rem;
        }

        .performance-row {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;
        }

        .category {
          font-size: 0.875rem;
          color: #4b5563;
          min-width: 120px;
          margin-right: 1rem;
        }

        .bar-container {
          flex: 1;
          height: 0.5rem;
          background: #e5e7eb;
          border-radius: 0.25rem;
          overflow: hidden;
        }

        .bar {
          height: 100%;
          background: #4f46e5;
          border-radius: 0.25rem;
        }

        .score {
          font-size: 0.75rem;
          color: #6b7280;
          min-width: 40px;
          text-align: right;
          margin-left: 0.5rem;
        }

        .action-buttons {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          margin-top: 1rem;
        }

        .view-results,
        .download-certificate,
        .download-results {
          padding: 0.75rem;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          text-align: center;
        }

        .view-results {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .view-results:hover {
          background: #4338ca;
        }

        .download-certificate {
          background: #10b981;
          color: white;
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .download-certificate:hover {
          background: #059669;
        }

        .download-results {
          background: #f3f4f6;
          color: #4b5563;
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .download-results:hover {
          background: #e5e7eb;
        }

        .download-certificate .icon,
        .download-results .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .proctoring-note {
          display: flex;
          align-items: flex-start;
          background: #f8fafc;
          border-radius: 0.375rem;
          padding: 1rem;
          border: 1px solid #e2e8f0;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .proctoring-note .icon {
          width: 1.25rem;
          height: 1.25rem;
          color: #64748b;
          margin-right: 0.75rem;
          flex-shrink: 0;
        }

        .proctoring-note p {
          margin: 0;
        }

        .proctoring-note strong {
          color: #1f2937;
        }

        @media (max-width: 640px) {
          .submission-content {
            padding: 1.5rem;
          }

          .performance-row {
            flex-direction: column;
            align-items: flex-start;
          }

          .category {
            margin-bottom: 0.25rem;
          }

          .bar-container {
            width: 100%;
            margin-bottom: 0.25rem;
          }
        }
      `}</style>
    </div>
  );
};

export default Submitted;