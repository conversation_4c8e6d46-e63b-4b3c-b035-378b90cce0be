import { useState } from 'react';
import { ChevronDownIcon, ChevronRightIcon, CheckIcon, XIcon, PlusIcon, TrashIcon } from 'lucide-react';

const Roles = () => {
  const [roles, setRoles] = useState([
    {
      id: 1,
      name: 'Student',
      description: 'Basic access to take exams and view results',
      permissions: [
        { resource: 'Exams', actions: ['read', 'take'] },
        { resource: 'Results', actions: ['read'] }
      ],
      expanded: false
    },
    {
      id: 2,
      name: 'Instructor',
      description: 'Can create exams and view all student results',
      permissions: [
        { resource: 'Exams', actions: ['create', 'read', 'update'] },
        { resource: 'Results', actions: ['read'] },
        { resource: 'Questions', actions: ['create', 'read', 'update', 'delete'] }
      ],
      expanded: false
    },
    {
      id: 3,
      name: 'Proctor',
      description: 'Can monitor exams in progress',
      permissions: [
        { resource: 'Exams', actions: ['read', 'monitor'] },
        { resource: 'Sessions', actions: ['read', 'intervene'] }
      ],
      expanded: false
    },
    {
      id: 4,
      name: 'Admin',
      description: 'Full system access including user management',
      permissions: [
        { resource: '*', actions: ['*'] }
      ],
      expanded: false
    }
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  // type Permission = { resource: string; actions: string[] };
  const [newRole, setNewRole] = useState<{
    name: string;
    description: string;
    permissions: Permission[];
  }>({
    name: '',
    description: '',
    permissions: []
  });
  const [availableResources] = useState([
    'Exams', 'Questions', 'Results', 'Users', 'Roles', 'Sessions', 'System'
  ]);
  const [availableActions] = useState([
    'create', 'read', 'update', 'delete', 'take', 'monitor', 'intervene', 'manage'
  ]);

const toggleRoleExpansion = (roleId: number): void => {
    setRoles(roles.map((role: Role) => 
        role.id === roleId ? { ...role, expanded: !role.expanded } : role
    ));
};

interface AddPermissionParams {
    roleId: number;
    resource: string;
    action: string;
}

const addPermission = (roleId: number, resource: string, action: string): void => {
    setRoles(roles.map((role: Role) => {
        if (role.id !== roleId) return role;
        
        const existingResource = role.permissions.find((p: Permission) => p.resource === resource);
        if (existingResource) {
            if (!existingResource.actions.includes(action)) {
                existingResource.actions.push(action);
            }
        } else {
            role.permissions.push({ resource, actions: [action] });
        }
        
        return { ...role };
    }));
};


const removePermission = (
    roleId: number,
    resource: string,
    action: string
): void => {
    setRoles(roles.map((role: Role) => {
        if (role.id !== roleId) return role;

        const resourceIndex = role.permissions.findIndex((p: Permission) => p.resource === resource);
        if (resourceIndex >= 0) {
            const actionIndex = role.permissions[resourceIndex].actions.indexOf(action);
            if (actionIndex >= 0) {
                role.permissions[resourceIndex].actions.splice(actionIndex, 1);
                if (role.permissions[resourceIndex].actions.length === 0) {
                    role.permissions.splice(resourceIndex, 1);
                }
            }
        }

        return { ...role };
    }));
};

  const handleAddRole = () => {
    const newId = Math.max(...roles.map(r => r.id), 0) + 1;
    setRoles([...roles, {
      id: newId,
      name: newRole.name,
      description: newRole.description,
      permissions: newRole.permissions,
      expanded: false
    }]);
    setNewRole({ name: '', description: '', permissions: [] });
    setShowAddModal(false);
  };

interface Permission {
    resource: string;
    actions: string[];
}

interface Role {
    id: number;
    name: string;
    description: string;
    permissions: Permission[];
    expanded: boolean;
}

const deleteRole = (roleId: number) => {
    setRoles(roles.filter((role: Role) => role.id !== roleId));
};

  return (
    <div className="roles-container">
      <div className="roles-content">
        <div className="roles-header">
          <h1>Roles & Permissions</h1>
          <button
            onClick={() => setShowAddModal(true)}
            className="add-role-button"
          >
            <PlusIcon className="icon" />
            Add Role
          </button>
        </div>

        <div className="roles-list">
          {roles.map(role => (
            <div key={role.id} className="role-card">
              <div 
                className="role-header"
                onClick={() => toggleRoleExpansion(role.id)}
              >
                <div className="role-title">
                  {role.expanded ? <ChevronDownIcon className="chevron" /> : <ChevronRightIcon className="chevron" />}
                  <h2>{role.name}</h2>
                  <span className="role-description">{role.description}</span>
                </div>
                <div className="role-actions">
                  <button 
                    className="delete-role-button"
                    title={`Delete role ${role.name}`}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteRole(role.id);
                    }}
                  >
                    <TrashIcon className="icon" />
                  </button>
                </div>
              </div>

              {role.expanded && (
                <div className="permissions-section">
                  <h3>ACL Rules</h3>
                  <div className="permissions-grid">
                    {availableResources.map(resource => (
                      <div key={resource} className="resource-row">
                        <div className="resource-name">{resource}</div>
                        <div className="action-buttons">
                          {availableActions.map(action => {
                            const hasPermission = role.permissions.some(p => 
                              (p.resource === resource || p.resource === '*') && 
                              (p.actions.includes(action) || p.actions.includes('*'))
                            );
                            return (
                              <button
                                key={action}
                                className={`action-button ${hasPermission ? 'granted' : ''}`}
                                onClick={() => 
                                  hasPermission 
                                    ? removePermission(role.id, resource, action)
                                    : addPermission(role.id, resource, action)
                                }
                              >
                                {action}
                                {hasPermission && <CheckIcon className="check-icon" />}
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Add Role Modal */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-content">
              <h2>Add New Role</h2>
              <div className="form-group">
                <label>Role Name</label>
                <input
                  type="text"
                  value={newRole.name}
                  onChange={(e) => setNewRole({...newRole, name: e.target.value})}
                  placeholder="e.g., Content Creator"
                />
              </div>
              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={newRole.description}
                  onChange={(e) => setNewRole({...newRole, description: e.target.value})}
                  placeholder="Describe the role's purpose"
                />
              </div>
              <div className="form-group">
                <label>Initial Permissions</label>
                <div className="permissions-selection">
                  {availableResources.map(resource => (
                    <div key={resource} className="resource-section">
                      <h4>{resource}</h4>
                      <div className="action-buttons">
                        {availableActions.map(action => (
                          <button
                            key={action}
                            className={`action-button ${
                              newRole.permissions.some(p => p.resource === resource && p.actions.includes(action)) 
                                ? 'granted' 
                                : ''
                            }`}
                            onClick={() => {
                              const existingPerm = newRole.permissions.find(p => p.resource === resource);
                              if (existingPerm) {
                                if (existingPerm.actions.includes(action)) {
                                  existingPerm.actions = existingPerm.actions.filter(a => a !== action);
                                  if (existingPerm.actions.length === 0) {
                                    setNewRole({
                                      ...newRole,
                                      permissions: newRole.permissions.filter(p => p.resource !== resource)
                                    });
                                  }
                                } else {
                                  existingPerm.actions.push(action);
                                }
                              } else {
                                setNewRole({
                                  ...newRole,
                                  permissions: [...newRole.permissions, { resource, actions: [action] }]
                                });
                              }
                            }}
                          >
                            {action}
                            {newRole.permissions.some(p => p.resource === resource && p.actions.includes(action)) && (
                              <CheckIcon className="check-icon" />
                            )}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button
                onClick={() => setShowAddModal(false)}
                className="cancel-button"
              >
                Cancel
              </button>
              <button
                onClick={handleAddRole}
                className="confirm-button"
                disabled={!newRole.name}
              >
                Create Role
              </button>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .roles-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .roles-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .roles-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .roles-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .add-role-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.5rem;
          font-weight: 500;
          cursor: pointer;
          transition: background 0.2s;
        }

        .add-role-button:hover {
          background: #4338ca;
        }

        .add-role-button .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .roles-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .role-card {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }

        .role-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          cursor: pointer;
          transition: background 0.2s;
        }

        .role-header:hover {
          background: #f9fafb;
        }

        .role-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          flex: 1;
        }

        .chevron {
          width: 1.25rem;
          height: 1.25rem;
          color: #6b7280;
        }

        .role-title h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
          min-width: 120px;
        }

        .role-description {
          color: #6b7280;
          font-size: 0.875rem;
        }

        .role-actions {
          display: flex;
          gap: 0.5rem;
        }

        .delete-role-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          background: #fee2e2;
          color: #b91c1c;
          border: none;
          cursor: pointer;
          transition: all 0.2s;
        }

        .delete-role-button:hover {
          background: #fecaca;
        }

        .delete-role-button .icon {
          width: 1rem;
          height: 1rem;
        }

        .permissions-section {
          padding: 1.5rem;
          border-top: 1px solid #e5e7eb;
        }

        .permissions-section h3 {
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .permissions-grid {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .resource-row {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .resource-name {
          font-weight: 500;
          color: #1f2937;
          min-width: 120px;
        }

        .action-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .action-button {
          position: relative;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          border: 1px solid #d1d5db;
          background: white;
          color: #6b7280;
          font-size: 0.75rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .action-button:hover {
          background: #f3f4f6;
        }

        .action-button.granted {
          background: #dcfce7;
          border-color: #86efac;
          color: #166534;
        }

        .check-icon {
          position: absolute;
          top: -0.5rem;
          right: -0.5rem;
          width: 1rem;
          height: 1rem;
          background: #10b981;
          color: white;
          border-radius: 50%;
          padding: 0.125rem;
        }

        /* Modal Styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          width: 100%;
          max-width: 800px;
          max-height: 90vh;
          overflow-y: auto;
        }

        .modal-content {
          padding: 1.5rem;
        }

        .modal-content h2 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1.5rem;
        }

        .form-group {
          margin-bottom: 1.5rem;
        }

        .form-group label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
          margin-bottom: 0.5rem;
        }

        .form-group input, .form-group textarea {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-group textarea {
          min-height: 80px;
        }

        .form-group input:focus, .form-group textarea:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .permissions-selection {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
          max-height: 400px;
          overflow-y: auto;
          padding: 0.5rem;
        }

        .resource-section {
          background: #f9fafb;
          border-radius: 0.5rem;
          padding: 1rem;
        }

        .resource-section h4 {
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.75rem 0;
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          padding: 1rem 1.5rem;
          background: #f9fafb;
          border-top: 1px solid #e5e7eb;
        }

        .modal-footer button {
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s;
        }

        .cancel-button {
          border: 1px solid #d1d5db;
          background: white;
          color: #374151;
        }

        .cancel-button:hover {
          background: #f3f4f6;
        }

        .confirm-button {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .confirm-button:hover {
          background: #4338ca;
        }

        .confirm-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        @media (max-width: 768px) {
          .resource-row {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
          }

          .modal {
            max-width: 95%;
          }
        }
      `}</style>
    </div>
  );
};

export default Roles;