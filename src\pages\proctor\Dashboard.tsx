import { Search, Filter, Clock, Eye, AlertCircle, CheckCircle, XCircle, ChevronDown } from 'lucide-react';
import { useState } from 'react';

const Dashboard = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [statusFilter, setStatusFilter] = useState('All');
  const [sortBy, setSortBy] = useState('Recent');

  const sessions = [
    { 
      id: 'sess01', 
      name: 'Math Final Exam', 
      status: 'Live', 
      date: '2023-11-15', 
      time: '10:00 AM - 12:00 PM',
      students: 42,
      flags: 3,
      subject: 'Mathematics',
      duration: '2 hours'
    },
    { 
      id: 'sess02', 
      name: 'CS101 Midterm', 
      status: 'Upcoming', 
      date: '2023-11-17', 
      time: '2:00 PM - 3:30 PM',
      students: 35,
      flags: 0,
      subject: 'Computer Science',
      duration: '1.5 hours'
    },
    { 
      id: 'sess03', 
      name: 'Physics Quiz', 
      status: 'Completed', 
      date: '2023-11-10', 
      time: '9:00 AM - 10:00 AM',
      students: 28,
      flags: 1,
      subject: 'Physics',
      duration: '1 hour'
    },
    { 
      id: 'sess04', 
      name: 'Chemistry Lab Exam', 
      status: 'Live', 
      date: '2023-11-15', 
      time: '1:00 PM - 3:00 PM',
      students: 24,
      flags: 2,
      subject: 'Chemistry',
      duration: '2 hours'
    },
  ];

  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         session.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All' || session.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const sortedSessions = [...filteredSessions].sort((a, b) => {
    if (sortBy === 'Recent') {
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    } else if (sortBy === 'Name') {
      return a.name.localeCompare(b.name);
    } else if (sortBy === 'Students') {
      return b.students - a.students;
    }
    return 0;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Live':
        return <AlertCircle size={16} className="text-red-500" />;
      case 'Upcoming':
        return <Clock size={16} className="text-amber-500" />;
      case 'Completed':
        return <CheckCircle size={16} className="text-green-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="dashboard-page">
      <div className="dashboard-header">
        <h1 className="dashboard-title">
          <Eye size={28} className="header-icon" />
          Proctor Dashboard
        </h1>
      </div>

      <div className="search-filter-container">
        <div className="search-filter-group">
          <div className="search-box">
            <Search className="search-icon" size={18} />
            <input
              type="text"
              placeholder="Search sessions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="filter-toggle-button"
          >
            <Filter size={18} className="button-icon" />
            Filters
            {showFilters ? <ChevronDown size={18} className="transform rotate-180" /> : <ChevronDown size={18} />}
          </button>
        </div>

        {showFilters && (
          <div className="advanced-filters">
            <div className="filter-group">
              <label htmlFor="status-filter">Status</label>
              <select
                id="status-filter"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="All">All Statuses</option>
                <option value="Live">Live</option>
                <option value="Upcoming">Upcoming</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
            <div className="filter-group">
              <label htmlFor="sort-by">Sort By</label>
              <select
                id="sort-by"
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
              >
                <option value="Recent">Most Recent</option>
                <option value="Name">Name (A-Z)</option>
                <option value="Students">Student Count</option>
              </select>
            </div>
          </div>
        )}
      </div>

      <div className="sessions-grid">
        {sortedSessions.length === 0 ? (
          <div className="empty-state">
            <XCircle size={48} className="text-gray-400 mx-auto" />
            <p>No sessions found matching your criteria</p>
          </div>
        ) : (
          sortedSessions.map((session) => (
            <div key={session.id} className={`session-card ${session.status.toLowerCase()}`}>
              <div className="session-header">
                <h2 className="session-name">{session.name}</h2>
                <div className={`session-status ${session.status.toLowerCase()}`}>
                  {getStatusIcon(session.status)}
                  <span>{session.status}</span>
                </div>
              </div>
              
              <div className="session-details">
                <div className="detail-row">
                  <span className="detail-label">Subject:</span>
                  <span className="detail-value">{session.subject}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Date & Time:</span>
                  <span className="detail-value">{session.date} • {session.time}</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Duration:</span>
                  <span className="detail-value">{session.duration}</span>
                </div>
              </div>
              
              <div className="session-footer">
                <div className="session-metrics">
                  <div className="metric">
                    <span className="metric-label">Students</span>
                    <span className="metric-value">{session.students}</span>
                  </div>
                  <div className="metric">
                    <span className="metric-label">Flags</span>
                    <span className={`metric-value ${session.flags > 0 ? 'text-red-500' : 'text-green-500'}`}>
                      {session.flags}
                    </span>
                  </div>
                </div>
                <a 
                  href={`/proctor/session/${session.id}`} 
                  className="view-session-button"
                >
                  <Eye size={16} className="button-icon" />
                  {session.status === 'Upcoming' ? 'Prepare' : 'View'} Session
                </a>
              </div>
            </div>
          ))
        )}
      </div>

      <style>{`
        .dashboard-page {
          padding: 2rem;
          max-width: 1400px;
          margin: 0 auto;
        }

        .dashboard-header {
          margin-bottom: 2rem;
        }

        .dashboard-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .header-icon {
          color: #15803d;
        }

        .search-filter-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1rem;
          margin-bottom: 1.5rem;
        }

        .search-filter-group {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
          .search-filter-group {
            flex-direction: row;
          }
        }

        .search-box {
          position: relative;
          flex-grow: 1;
        }

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #64748b;
        }

        .search-box input {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .search-box input:focus {
          outline: none;
          border-color: #86efac;
          box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
        }

        .filter-toggle-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background: none;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-weight: 500;
          color: #64748b;
          cursor: pointer;
          transition: all 0.2s;
        }

        .filter-toggle-button:hover {
          background-color: #f8fafc;
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        .advanced-filters {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1rem;
          padding-top: 1rem;
          border-top: 1px solid #e2e8f0;
        }

        @media (min-width: 768px) {
          .advanced-filters {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        .filter-group {
          margin-bottom: 1rem;
        }

        .filter-group label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .filter-group select {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
        }

        .sessions-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 1.5rem;
        }

        .session-card {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.5rem;
          border-top: 4px solid;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .session-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .session-card.live {
          border-top-color: #ef4444;
        }

        .session-card.upcoming {
          border-top-color: #f59e0b;
        }

        .session-card.completed {
          border-top-color: #10b981;
        }

        .session-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 1rem;
        }

        .session-name {
          font-weight: 600;
          font-size: 1.125rem;
          color: #1f2937;
          margin-right: 0.5rem;
        }

        .session-status {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .session-status.live {
          background-color: #fee2e2;
          color: #b91c1c;
        }

        .session-status.upcoming {
          background-color: #fef3c7;
          color: #92400e;
        }

        .session-status.completed {
          background-color: #dcfce7;
          color: #166534;
        }

        .session-details {
          margin-bottom: 1.5rem;
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
        }

        .detail-label {
          color: #64748b;
        }

        .detail-value {
          font-weight: 500;
          color: #1f2937;
          text-align: right;
        }

        .session-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #e5e7eb;
          padding-top: 1rem;
        }

        .session-metrics {
          display: flex;
          gap: 1rem;
        }

        .metric {
          display: flex;
          flex-direction: column;
        }

        .metric-label {
          font-size: 0.75rem;
          color: #64748b;
        }

        .metric-value {
          font-weight: 600;
          font-size: 1rem;
        }

        .view-session-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .view-session-button:hover {
          background-color: #166534;
        }

        .empty-state {
          grid-column: 1 / -1;
          text-align: center;
          padding: 3rem;
          color: #64748b;
        }

        .empty-state p {
          margin-top: 1rem;
          font-size: 1.125rem;
        }
      `}</style>
    </div>
  );
};

export default Dashboard;