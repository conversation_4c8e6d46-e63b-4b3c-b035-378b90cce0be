import { useState } from 'react';
import { ChartBarIcon, ClipboardListIcon, ArrowUpIcon, EyeIcon, DownloadIcon, ClockIcon } from 'lucide-react';

const Results = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [expandedResult, setExpandedResult] = useState<number | null>(null);

  const results: Result[] = [
    { 
      id: 1,
      title: 'JavaScript Fundamentals Certification', 
      score: 88, 
      passingScore: 70,
      date: '2023-05-15',
      feedback: 'Excellent understanding of core JavaScript concepts. Your performance in DOM manipulation was particularly strong. Consider reviewing closure patterns for even better scores.',
      topics: [
        { name: 'Variables & Data Types', score: 95 },
        { name: 'Functions & Scope', score: 85 },
        { name: 'DOM Manipulation', score: 92 },
        { name: 'Async Programming', score: 80 },
        { name: 'ES6 Features', score: 82 }
      ],
      percentile: 92,
      timeSpent: '45 minutes',
      status: 'passed'
    },
    { 
      id: 2,
      title: 'Python Advanced Concepts', 
      score: 74, 
      passingScore: 75,
      date: '2023-04-28',
      feedback: 'Good effort, but needs improvement in OOP concepts and decorators. Your work with data structures was solid. Practice with generator functions would be beneficial.',
      topics: [
        { name: 'OOP Concepts', score: 65 },
        { name: 'Decorators', score: 60 },
        { name: 'Generators', score: 70 },
        { name: 'Data Structures', score: 85 },
        { name: 'Async IO', score: 75 }
      ],
      percentile: 68,
      timeSpent: '78 minutes',
      status: 'failed'
    },
    { 
      id: 3,
      title: 'Database Design Mastery', 
      score: 91, 
      passingScore: 80,
      date: '2023-03-10',
      feedback: 'Outstanding performance across all database concepts. Your normalization skills are exceptional. You might explore NoSQL databases to expand your expertise.',
      topics: [
        { name: 'Normalization', score: 98 },
        { name: 'SQL Queries', score: 95 },
        { name: 'Indexing', score: 89 },
        { name: 'Transactions', score: 85 },
        { name: 'Schema Design', score: 90 }
      ],
      percentile: 97,
      timeSpent: '112 minutes',
      status: 'passed'
    }
  ];

  const filteredResults = activeTab === 'all' 
    ? results 
    : results.filter(res => activeTab === 'passed' ? res.status === 'passed' : res.status === 'failed');

interface Topic {
    name: string;
    score: number;
}

interface Result {
    id: number;
    title: string;
    score: number;
    passingScore: number;
    date: string;
    feedback: string;
    topics: Topic[];
    percentile: number;
    timeSpent: string;
    status: 'passed' | 'failed';
}

const toggleExpand = (id: number) => {
    setExpandedResult(expandedResult === id ? null : id);
};

interface DownloadableResult {
    id: number;
    title: string;
    score: number;
    passingScore: number;
    date: string;
    feedback: string;
    topics: Topic[];
    percentile: number;
    timeSpent: string;
    status: 'passed' | 'failed';
}

const downloadResult = (result: DownloadableResult) => {
    // In a real app, this would generate a PDF
    alert(`Downloading results for ${result.title}`);
};

  return (
    <div className="results-container">
      <div className="results-content">
        <div className="results-header">
          <h1>Your Exam Results</h1>
          <div className="results-actions">
            <button className="refresh-button">
              <ArrowUpIcon className="icon" />
              Refresh
            </button>
          </div>
        </div>

        <div className="results-tabs">
          <button 
            className={`tab ${activeTab === 'all' ? 'active' : ''}`}
            onClick={() => setActiveTab('all')}
          >
            All Results
          </button>
          <button 
            className={`tab ${activeTab === 'passed' ? 'active' : ''}`}
            onClick={() => setActiveTab('passed')}
          >
            Passed
          </button>
          <button 
            className={`tab ${activeTab === 'failed' ? 'active' : ''}`}
            onClick={() => setActiveTab('failed')}
          >
            Failed
          </button>
        </div>

        <div className="results-grid">
          {filteredResults.length > 0 ? (
            filteredResults.map((result) => (
              <div key={result.id} className={`result-card ${result.status}`}>
                <div 
                  className="result-summary"
                  onClick={() => toggleExpand(result.id)}
                >
                  <div className="result-title">
                    <h2>{result.title}</h2>
                    <span className="exam-date">{new Date(result.date).toLocaleDateString()}</span>
                  </div>
                  <div className="result-score">
                    <div className={`score-circle ${result.status}`}>
                      {result.score}%
                    </div>
                    <div className="passing-score">
                      Passing: {result.passingScore}%
                    </div>
                  </div>
                  <div className="result-meta">
                    <span className="percentile">
                      <ChartBarIcon className="icon" />
                      Top {result.percentile}%
                    </span>
                    <span className="time-spent">
                      <ClockIcon className="icon" />
                      {result.timeSpent}
                    </span>
                  </div>
                </div>

                {expandedResult === result.id && (
                  <div className="result-details">
                    <div className="detail-section">
                      <h3>
                        <ClipboardListIcon className="icon" />
                        Topic Breakdown
                      </h3>
                      <div className="topics-grid">
                        {result.topics.map((topic, index) => (
                          <div key={index} className="topic-item">
                            <div className="topic-name">{topic.name}</div>
                            <div className="topic-score">
                              <div 
                                className="score-bar" 
                                style={{ width: `${topic.score}%` }}
                              ></div>
                              <span>{topic.score}%</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="detail-section">
                      <h3>Instructor Feedback</h3>
                      <div className="feedback-content">
                        {result.feedback}
                      </div>
                    </div>

                    <div className="result-actions">
                      <button className="view-exam">
                        <EyeIcon className="icon" />
                        View Exam
                      </button>
                      <button 
                        className="download-result"
                        onClick={() => downloadResult(result)}
                      >
                        <DownloadIcon className="icon" />
                        Download Results
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="no-results">
              No results match your current filters
            </div>
          )}
        </div>
      </div>

      <style>{`
        .results-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .results-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .results-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .results-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .results-actions {
          display: flex;
          gap: 0.75rem;
        }

        .refresh-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          background: #f3f4f6;
          color: #4b5563;
          border: none;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .refresh-button:hover {
          background: #e5e7eb;
        }

        .refresh-button .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .results-tabs {
          display: flex;
          border-bottom: 1px solid #e5e7eb;
          margin-bottom: 1.5rem;
        }

        .tab {
          padding: 0.75rem 1.5rem;
          border: none;
          background: transparent;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        }

        .tab:hover {
          color: #4f46e5;
        }

        .tab.active {
          color: #4f46e5;
          border-bottom-color: #4f46e5;
        }

        .results-grid {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .result-card {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          overflow: hidden;
        }

        .result-card.passed {
          border-left: 4px solid #10b981;
        }

        .result-card.failed {
          border-left: 4px solid #ef4444;
        }

        .result-summary {
          display: grid;
          grid-template-columns: 2fr 1fr 1fr;
          gap: 1rem;
          padding: 1.5rem;
          cursor: pointer;
          transition: background 0.2s;
        }

        .result-summary:hover {
          background: #f9fafb;
        }

        .result-title {
          display: flex;
          flex-direction: column;
        }

        .result-title h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.25rem 0;
        }

        .exam-date {
          font-size: 0.875rem;
          color: #6b7280;
        }

        .result-score {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }

        .score-circle {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 1.125rem;
        }

        .score-circle.passed {
          background: #dcfce7;
          color: #166534;
          border: 3px solid #86efac;
        }

        .score-circle.failed {
          background: #fee2e2;
          color: #b91c1c;
          border: 3px solid #fca5a5;
        }

        .passing-score {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.25rem;
        }

        .result-meta {
          display: flex;
          flex-direction: column;
          justify-content: center;
          gap: 0.5rem;
        }

        .percentile, .time-spent {
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .percentile .icon, .time-spent .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
          color: #6b7280;
        }

        .result-details {
          padding: 0 1.5rem 1.5rem;
          border-top: 1px solid #f3f4f6;
        }

        .detail-section {
          margin-bottom: 1.5rem;
        }

        .detail-section h3 {
          display: flex;
          align-items: center;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .detail-section h3 .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          color: #4f46e5;
        }

        .topics-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
          gap: 1rem;
        }

        .topic-item {
          margin-bottom: 0.5rem;
        }

        .topic-name {
          font-size: 0.875rem;
          color: #4b5563;
          margin-bottom: 0.25rem;
        }

        .topic-score {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .score-bar {
          height: 6px;
          background: #4f46e5;
          border-radius: 3px;
          flex-grow: 1;
        }

        .topic-score span {
          font-size: 0.75rem;
          color: #6b7280;
          min-width: 40px;
        }

        .feedback-content {
          background: #f8fafc;
          border-radius: 0.5rem;
          padding: 1rem;
          font-size: 0.875rem;
          line-height: 1.5;
          color: #4b5563;
        }

        .result-actions {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
          margin-top: 1.5rem;
        }

        .view-exam, .download-result {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .view-exam {
          background: #e0f2fe;
          color: #0369a1;
          border: none;
        }

        .view-exam:hover {
          background: #bae6fd;
        }

        .download-result {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .download-result:hover {
          background: #4338ca;
        }

        .view-exam .icon, .download-result .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .no-results {
          text-align: center;
          padding: 3rem;
          color: #6b7280;
          font-size: 0.875rem;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
          .result-summary {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .results-tabs {
            overflow-x: auto;
            padding-bottom: 0.5rem;
          }

          .tab {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
          }

          .topics-grid {
            grid-template-columns: 1fr;
          }

          .result-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default Results;