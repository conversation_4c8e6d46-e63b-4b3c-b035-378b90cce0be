import React, { useState } from 'react';
import type { JSX } from 'react';
import { ClockIcon, CalendarIcon, CheckCircleIcon, XCircleIcon, PlayIcon, ChartBarIcon, ClipboardListIcon } from 'lucide-react';
const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('upcoming');
  const [filter, setFilter] = useState('all');
  
  const exams: Exam[] = [
    { 
      id: 'js101', 
      title: 'JavaScript Basics Certification', 
      status: 'upcoming', 
      date: '2025-06-01T09:00:00Z',
      duration: 90,
      totalQuestions: 30,
      passingScore: 70,
      enrolled: true,
      instructor: 'Dr. <PERSON>',
      description: 'Covers fundamental JavaScript concepts including variables, functions, and DOM manipulation.'
    },
    { 
      id: 'py202', 
      title: 'Python Advanced Techniques', 
      status: 'ongoing', 
      date: '2025-06-10T14:00:00Z',
      duration: 120,
      totalQuestions: 45,
      passingScore: 75,
      enrolled: true,
      instructor: 'Prof<PERSON> <PERSON>',
      description: 'Advanced Python programming including decorators, generators, and async programming.'
    },
    { 
      id: 'db301', 
      title: 'Database Design Mastery', 
      status: 'completed', 
      date: '2025-05-15T10:00:00Z',
      duration: 180,
      totalQuestions: 50,
      passingScore: 80,
      score: 85,
      enrolled: true,
      instructor: 'Dr. Emily <PERSON>',
      description: 'Comprehensive database design principles and SQL optimization techniques.'
    },
    { 
      id: 'ai401', 
      title: 'AI Fundamentals', 
      status: 'upcoming', 
      date: '2025-06-20T13:30:00Z',
      duration: 150,
      totalQuestions: 40,
      passingScore: 70,
      enrolled: false,
      instructor: 'Prof. David Kim',
      description: 'Introduction to machine learning algorithms and neural networks.'
    },
  ];

  const filteredExams = exams.filter(exam => {
    if (filter === 'enrolled' && !exam.enrolled) return false;
    if (activeTab === 'upcoming') return exam.status === 'upcoming';
    if (activeTab === 'ongoing') return exam.status === 'ongoing';
    if (activeTab === 'completed') return exam.status === 'completed';
    return true;
  });

  interface FormatDateOptions {
    month?: 'short' | 'long' | 'numeric' | '2-digit' | 'narrow';
    day?: 'numeric' | '2-digit';
    year?: 'numeric' | '2-digit';
    hour?: '2-digit' | 'numeric';
    minute?: '2-digit' | 'numeric';
  }

  const formatDate = (dateString: string, options?: FormatDateOptions): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  };

  interface StatusBadgeProps {
    status: 'upcoming' | 'ongoing' | 'completed' | string;
  }

  const getStatusBadge = (status: StatusBadgeProps['status']): JSX.Element => {
    switch (status) {
      case 'upcoming':
        return <span className="status-badge upcoming"><ClockIcon className="icon" /> Upcoming</span>;
      case 'ongoing':
        return <span className="status-badge ongoing"><PlayIcon className="icon" /> Ongoing</span>;
      case 'completed':
        return <span className="status-badge completed"><CheckCircleIcon className="icon" /> Completed</span>;
      default:
        return <span className="status-badge">{status}</span>;
    }
  };

  interface Exam {
    id: string;
    title: string;
    status: 'upcoming' | 'ongoing' | 'completed';
    date: string;
    duration: number;
    totalQuestions: number;
    passingScore: number;
    enrolled: boolean;
    instructor: string;
    description: string;
    score?: number;
  }

  interface ScoreBadgeProps {
    exam: Exam;
  }

  const getScoreBadge = (exam: Exam): JSX.Element | null => {
    if (exam.status !== 'completed') return null;
    const passed = (exam.score ?? 0) >= exam.passingScore;
    return (
      <span className={`score-badge ${passed ? 'passed' : 'failed'}`}>
        {passed ? <CheckCircleIcon className="icon" /> : <XCircleIcon className="icon" />}
        {exam.score}% {passed ? 'Passed' : 'Failed'}
      </span>
    );
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-content">
        <div className="dashboard-header">
          <h1>Your Exams</h1>
          <div className="filter-controls">
            <button 
              className={`filter-button ${filter === 'all' ? 'active' : ''}`}
              onClick={() => setFilter('all')}
            >
              All Exams
            </button>
            <button 
              className={`filter-button ${filter === 'enrolled' ? 'active' : ''}`}
              onClick={() => setFilter('enrolled')}
            >
              My Enrollments
            </button>
          </div>
        </div>

        <div className="tabs">
          <button 
            className={`tab ${activeTab === 'upcoming' ? 'active' : ''}`}
            onClick={() => setActiveTab('upcoming')}
          >
            Upcoming
          </button>
          <button 
            className={`tab ${activeTab === 'ongoing' ? 'active' : ''}`}
            onClick={() => setActiveTab('ongoing')}
          >
            Ongoing
          </button>
          <button 
            className={`tab ${activeTab === 'completed' ? 'active' : ''}`}
            onClick={() => setActiveTab('completed')}
          >
            Completed
          </button>
        </div>

        <div className="exams-grid">
          {filteredExams.length > 0 ? (
            filteredExams.map((exam) => (
              <div key={exam.id} className="exam-card">
                <div className="exam-header">
                  <h2>{exam.title}</h2>
                  <div className="exam-meta">
                    {getStatusBadge(exam.status)}
                    {getScoreBadge(exam)}
                  </div>
                </div>

                <div className="exam-details">
                  <div className="detail-row">
                    <CalendarIcon className="icon" />
                    <span>{formatDate(exam.date)}</span>
                  </div>
                  <div className="detail-row">
                    <ClockIcon className="icon" />
                    <span>{exam.duration} minutes</span>
                  </div>
                  <div className="detail-row">
                    <ClipboardListIcon className="icon" />
                    <span>{exam.totalQuestions} questions</span>
                  </div>
                  <div className="detail-row">
                    <ChartBarIcon className="icon" />
                    <span>Passing score: {exam.passingScore}%</span>
                  </div>
                </div>

                <div className="exam-footer">
                  <div className="instructor">
                    Instructor: <span>{exam.instructor}</span>
                  </div>
                  <div className="actions">
                    {exam.status === 'upcoming' && !exam.enrolled && (
                      <button className="enroll-button">
                        Enroll Now
                      </button>
                    )}
                    {exam.status === 'upcoming' && exam.enrolled && (
                      <button className="view-button">
                        View Details
                      </button>
                    )}
                    {exam.status === 'ongoing' && (
                      <button className="start-button">
                        Continue Exam
                      </button>
                    )}
                    {exam.status === 'completed' && (
                      <button className="results-button">
                        View Results
                      </button>
                    )}
                  </div>
                </div>

                {exam.status === 'upcoming' && (
                  <div className="progress-container">
                    <div className="progress-bar" style={{ width: '75%' }}></div>
                    <span className="progress-text">75% of students completed preparation</span>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="no-exams">
              No exams match your current filters
            </div>
          )}
        </div>
      </div>

      <style>{`
        .dashboard-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .dashboard-content {
          max-width: 1200px;
          margin: 0 auto;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
        }

        .dashboard-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .filter-controls {
          display: flex;
          gap: 0.5rem;
          background: #f3f4f6;
          border-radius: 0.5rem;
          padding: 0.25rem;
        }

        .filter-button {
          padding: 0.5rem 1rem;
          border: none;
          background: transparent;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        }

        .filter-button.active {
          background: white;
          color: #4f46e5;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .tabs {
          display: flex;
          border-bottom: 1px solid #e5e7eb;
          margin-bottom: 1.5rem;
        }

        .tab {
          padding: 0.75rem 1.5rem;
          border: none;
          background: transparent;
          border-bottom: 3px solid transparent;
          font-weight: 500;
          color: #6b7280;
          cursor: pointer;
          transition: all 0.2s;
        }

        .tab:hover {
          color: #4f46e5;
        }

        .tab.active {
          color: #4f46e5;
          border-bottom-color: #4f46e5;
        }

        .exams-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 1.5rem;
        }

        .exam-card {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid rgba(0, 0, 0, 0.05);
          overflow: hidden;
          display: flex;
          flex-direction: column;
        }

        .exam-header {
          padding: 1.5rem 1.5rem 0;
        }

        .exam-header h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .exam-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .status-badge {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .status-badge.upcoming {
          background: #e0f2fe;
          color: #0369a1;
        }

        .status-badge.ongoing {
          background: #fef3c7;
          color: #92400e;
        }

        .status-badge.completed {
          background: #dcfce7;
          color: #166534;
        }

        .status-badge .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.25rem;
        }

        .score-badge {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
        }

        .score-badge.passed {
          background: #dcfce7;
          color: #166534;
        }

        .score-badge.failed {
          background: #fee2e2;
          color: #b91c1c;
        }

        .score-badge .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.25rem;
        }

        .exam-details {
          padding: 0 1.5rem;
          margin: 1rem 0;
        }

        .detail-row {
          display: flex;
          align-items: center;
          margin-bottom: 0.75rem;
          font-size: 0.875rem;
          color: #4b5563;
        }

        .detail-row .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
          color: #6b7280;
        }

        .exam-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          margin-top: auto;
          border-top: 1px solid #f3f4f6;
        }

        .instructor {
          font-size: 0.75rem;
          color: #6b7280;
        }

        .instructor span {
          font-weight: 500;
          color: #1f2937;
        }

        .actions button {
          padding: 0.375rem 0.75rem;
          border-radius: 0.375rem;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .enroll-button {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .enroll-button:hover {
          background: #4338ca;
        }

        .view-button {
          background: #f3f4f6;
          color: #4b5563;
          border: none;
        }

        .view-button:hover {
          background: #e5e7eb;
        }

        .start-button {
          background: #10b981;
          color: white;
          border: none;
        }

        .start-button:hover {
          background: #059669;
        }

        .results-button {
          background: #3b82f6;
          color: white;
          border: none;
        }

        .results-button:hover {
          background: #2563eb;
        }

        .progress-container {
          padding: 0.75rem 1.5rem;
          background: #f8fafc;
          border-top: 1px solid #f1f5f9;
        }

        .progress-bar {
          height: 0.5rem;
          background: #4f46e5;
          border-radius: 0.25rem;
          margin-bottom: 0.25rem;
        }

        .progress-text {
          font-size: 0.75rem;
          color: #64748b;
        }

        .no-exams {
          grid-column: 1 / -1;
          text-align: center;
          padding: 3rem;
          color: #6b7280;
          font-size: 0.875rem;
        }

        @media (max-width: 768px) {
          .dashboard-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 1rem;
          }

          .filter-controls {
            width: 100%;
          }

          .tabs {
            overflow-x: auto;
            padding-bottom: 0.5rem;
          }

          .tab {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
          }

          .exams-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Dashboard;