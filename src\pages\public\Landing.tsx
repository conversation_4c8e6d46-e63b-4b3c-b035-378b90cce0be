import React, { useState, useEffect } from 'react';
import { 
  ShieldCheck,
  Lock,
  BarChart2,
  ArrowRight,
  Users,
  Globe,
  Award,
  Zap,
  Brain,
  Target
} from 'lucide-react';

const Landing = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentStat, setCurrentStat] = useState(0);

  useEffect(() => {
    interface MousePosition {
      x: number;
      y: number;
    }

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
      x: (e.clientX / window.innerWidth) * 100,
      y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStat((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { number: '2.5M+', label: 'Students Protected', icon: Users },
    { number: '850+', label: 'Global Institutions', icon: Globe },
    { number: '99.97%', label: 'Accuracy Rate', icon: Target },
    { number: '24/7', label: 'AI Monitoring', icon: Brain }
  ];

  const teamMembers = [
    {
      name: 'Dr. Sarah Chen',
      role: 'Chief AI Architect',
      background: 'Former Google DeepMind researcher with 12+ years in ML security',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Security Engineering Lead',
      background: 'Ex-Pentagon cybersecurity specialist, ethical hacking champion',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Prof. Elena Vasquez',
      role: 'Education Technology Director',
      background: 'Harvard EdTech PhD, transformed 200+ university assessment systems',
      gradient: 'from-green-500 to-teal-500'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-white">
      <div className="flex-1 flex items-center justify-center p-8 bg-gradient-to-br from-indigo-100 to-indigo-200">
        <div className="max-w-3xl text-center p-8">
          <div className="mx-auto mb-6 w-20 h-20 flex items-center justify-center bg-white rounded-full shadow-lg text-primary-600">
            <ShieldCheck size={48} />
          </div>
          <h1 className="text-4xl font-extrabold text-primary-700 mb-4 leading-tight">Welcome to ExamProctor</h1>
          <p className="text-xl text-primary-800 mb-8 max-w-2xl mx-auto">Secure, smart, and scalable online exams</p>

          <div className="flex gap-4 justify-center mt-8">
            <a href="/signup" className="flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg font-semibold no-underline transition-all duration-200 shadow-md hover:bg-primary-700 hover:-translate-y-0.5 hover:shadow-lg">
              Get Started
              <ArrowRight size={18} />
            </a>
            <a href="/login" className="px-6 py-3 bg-white text-primary-600 rounded-lg font-semibold no-underline transition-all duration-200 shadow-md border border-primary-200 hover:bg-primary-50 hover:-translate-y-0.5">
              Login
            </a>
          </div>
        </div>
      </div>

      <div className="py-16 px-8 bg-gray-50">
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <ShieldCheck size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Secure Proctoring</h3>
            <p className="text-gray-600 leading-relaxed">
              Advanced AI monitoring to ensure exam integrity and prevent cheating.
            </p>
          </div>

          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <Lock size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Data Protection</h3>
            <p className="text-gray-600 leading-relaxed">
              End-to-end encryption and secure storage for all exam data.
            </p>
          </div>

          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <BarChart2 size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Detailed Analytics</h3>
            <p className="text-gray-600 leading-relaxed">
              Comprehensive reports and performance insights for students and instructors.
            </p>
          </div>
        </div>
      </div>

      <div className="relative py-20 px-8 bg-slate-900 overflow-hidden">
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(79, 70, 229, 0.15) 0%, transparent 50%)`
          }}
        />

        <div className="relative max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-6">
              <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-20"></div>
              <Award className="mx-4 text-primary-500" size={24} />
              <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-20"></div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Pioneering the Future of
              <span className="bg-gradient-to-r from-primary-400 to-purple-400 bg-clip-text text-transparent"> Secure Education</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Where cutting-edge AI meets uncompromising academic integrity
            </p>
          </div>

          <div className="mb-16">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div
                    key={index}
                    className={`relative p-6 bg-slate-800 rounded-xl border transition-all duration-300 ${
                      currentStat === index
                        ? 'border-primary-500 bg-slate-700 shadow-lg shadow-primary-500/20'
                        : 'border-slate-700 hover:border-slate-600'
                    }`}
                  >
                    <div className="flex items-center justify-center w-12 h-12 mb-4 bg-primary-600 rounded-lg text-white">
                      <IconComponent size={28} />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                      <div className="text-slate-400 text-sm">{stat.label}</div>
                    </div>
                    {currentStat === index && (
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-500 to-purple-500 rounded-xl opacity-20 animate-pulse"></div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="flex items-center text-2xl font-bold text-white mb-6">
                <Zap className="mr-3 text-primary-500 drop-shadow-lg" size={32} />
                Born from Academic Excellence
              </h3>
              <div className="space-y-6">
                <p className="text-lg leading-relaxed text-slate-300">
                  In 2019, when traditional education faced its greatest disruption, a team of visionary
                  researchers from MIT, Stanford, and Oxford united with a singular mission: to preserve
                  the sanctity of academic achievement in the digital age.
                </p>
                <p className="text-lg leading-relaxed text-slate-300">
                  What began as an emergency response to remote learning challenges has evolved into
                  the world's most sophisticated proctoring ecosystem. Our neural networks don't just
                  detect cheating—they understand intent, context, and behavior patterns with
                  unprecedented precision.
                </p>
                <p className="text-lg leading-relaxed text-slate-300">
                  Today, ExamProctor stands as the guardian of academic integrity for over 850
                  institutions worldwide, protecting the value of every degree, every certification,
                  and every achievement earned through genuine effort.
                </p>
              </div>

              <div className="grid grid-cols-3 gap-8 mt-12 p-8 bg-primary-600/10 rounded-2xl border border-primary-500/20">
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">47</div>
                  <div className="text-primary-300 text-sm font-medium">AI Patents</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">0.03%</div>
                  <div className="text-primary-300 text-sm font-medium">False Positive Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">&lt; 50ms</div>
                  <div className="text-primary-300 text-sm font-medium">Detection Latency</div>
                </div>
              </div>
            </div>

            <div className="flex justify-center items-center min-h-96">
              <div className="relative w-80 h-80">
                {/* Central brain icon */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center shadow-lg">
                  <div className="absolute inset-0 bg-primary-500 rounded-full animate-ping opacity-20"></div>
                  <Brain size={40} className="text-white relative z-10" />
                </div>

                {/* Network nodes */}
                {[...Array(8)].map((_, i) => {
                  const angle = (i * 45) * (Math.PI / 180);
                  const radius = 120;
                  const x = Math.cos(angle) * radius;
                  const y = Math.sin(angle) * radius;

                  return (
                    <div
                      key={i}
                      className="absolute w-4 h-4 bg-primary-400 rounded-full animate-pulse"
                      style={{
                        left: `calc(50% + ${x}px - 8px)`,
                        top: `calc(50% + ${y}px - 8px)`,
                        animationDelay: `${i * 0.2}s`
                      }}
                    >
                      <div className="absolute inset-0 bg-primary-300 rounded-full animate-ping opacity-40"></div>
                    </div>
                  );
                })}

                {/* Connection lines */}
                <svg className="absolute inset-0 w-full h-full">
                  {[...Array(8)].map((_, i) => {
                    const angle = (i * 45) * (Math.PI / 180);
                    const radius = 120;
                    const x = Math.cos(angle) * radius + 160;
                    const y = Math.sin(angle) * radius + 160;

                    return (
                      <line
                        key={i}
                        x1="160"
                        y1="160"
                        x2={x}
                        y2={y}
                        stroke="rgba(79, 70, 229, 0.3)"
                        strokeWidth="1"
                        className="animate-pulse"
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    );
                  })}
                </svg>
              </div>
            </div>
            </div>
          </div>

          <div className="mt-20">
            <h3 className="text-2xl font-bold text-white text-center mb-12">Meet the Visionaries</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {teamMembers.map((member, index) => (
                <div key={index} className="relative group">
                  <div className="bg-slate-800 p-6 rounded-xl border border-slate-700 transition-all duration-300 hover:border-primary-500 hover:shadow-lg hover:shadow-primary-500/20">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${member.gradient} flex items-center justify-center text-white text-xl font-bold`}>
                      {member.name.charAt(0)}
                    </div>
                    <div className="text-center">
                      <h4 className="text-lg font-semibold text-white mb-1">{member.name}</h4>
                      <div className="text-primary-400 text-sm font-medium mb-3">{member.role}</div>
                      <p className="text-slate-400 text-sm leading-relaxed">{member.background}</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-purple-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                </div>
              ))}
            </div>
          </div>

          <div className="mt-20 text-center">
            <div className="max-w-4xl mx-auto p-8 bg-slate-800/50 rounded-2xl border border-slate-700">
              <h3 className="text-2xl font-bold text-white mb-6">Our Unwavering Commitment</h3>
              <p className="text-lg text-slate-300 leading-relaxed mb-6 italic">
                "Every test taken, every assignment submitted, every achievement earned through
                ExamProctor represents genuine human potential unleashed. We don't just prevent
                cheating—we preserve the fundamental trust that makes education meaningful."
              </p>
              <div className="flex items-center justify-center">
                <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-32"></div>
                <span className="mx-4 text-primary-400 font-medium">The ExamProctor Team</span>
                <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-32"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .landing-page {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background-color: white;
        }

        .hero-section {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }

        .hero-content {
          max-width: 800px;
          text-align: center;
          padding: 2rem;
        }

        .logo-icon {
          margin: 0 auto 1.5rem;
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: white;
          border-radius: 50%;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
          color: #4f46e5;
        }

        .hero-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: #4338ca;
          margin-bottom: 1rem;
          line-height: 1.2;
        }

        .hero-subtitle {
          font-size: 1.25rem;
          color: #3730a3;
          margin-bottom: 2rem;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .cta-buttons {
          display: flex;
          gap: 1rem;
          justify-content: center;
          margin-top: 2rem;
        }

        .primary-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background-color: #4f46e5;
          color: white;
          border-radius: 0.5rem;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .primary-button:hover {
          background-color: #4338ca;
          transform: translateY(-1px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .secondary-button {
          padding: 0.75rem 1.5rem;
          background-color: white;
          color: #4f46e5;
          border: 2px solid #4f46e5;
          border-radius: 0.5rem;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.2s;
        }

        .secondary-button:hover {
          background-color: #ede9fe;
          transform: translateY(-1px);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .button-icon {
          transition: transform 0.2s;
        }

        .primary-button:hover .button-icon {
          transform: translateX(2px);
        }

        .features-section {
          padding: 4rem 2rem;
          background-color: white;
        }

        .features-container {
          max-width: 1200px;
          margin: 0 auto;
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 2rem;
        }

        @media (min-width: 768px) {
          .features-container {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .feature-card {
          padding: 2rem;
          border-radius: 0.5rem;
          background-color: #f0f5ff;
          text-align: center;
          transition: all 0.2s;
        }

        .feature-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 10px 15px -3px rgba(79, 70, 229, 0.2);
        }

        .feature-icon {
          width: 64px;
          height: 64px;
          margin: 0 auto 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #c7d2fe;
          color: #4f46e5;
          border-radius: 50%;
        }

        .feature-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: #3730a3;
          margin-bottom: 1rem;
        }

        .feature-description {
          color: #4c51bf;
          line-height: 1.5;
        }

        .about-section {
          position: relative;
          padding: 6rem 2rem;
          background: linear-gradient(135deg, #0f0f23 0%, #1a1a3e 50%, #0f0f23 100%);
          overflow: hidden;
        }

        .about-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          transition: background 0.3s ease;
        }

        .about-container {
          max-width: 1400px;
          margin: 0 auto;
          position: relative;
          z-index: 1;
        }

        .section-header {
          text-align: center;
          margin-bottom: 5rem;
        }

        .header-decoration {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .decoration-line {
          width: 80px;
          height: 2px;
          background: linear-gradient(90deg, transparent, #4f46e5, transparent);
        }

        .decoration-icon {
          color: #4f46e5;
          filter: drop-shadow(0 0 10px rgba(79, 70, 229, 0.5));
        }

        .section-title {
          font-size: 3.5rem;
          font-weight: 900;
          color: white;
          margin-bottom: 1.5rem;
          line-height: 1.1;
        }

        .gradient-text {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
          font-size: 1.5rem;
          color: #a5b4fc;
          max-width: 800px;
          margin: 0 auto;
          font-weight: 300;
        }

        .stats-showcase {
          margin-bottom: 6rem;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .stat-card {
          position: relative;
          padding: 2.5rem;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(79, 70, 229, 0.2);
          border-radius: 1.5rem;
          display: flex;
          align-items: center;
          gap: 1.5rem;
          transition: all 0.4s ease;
          overflow: hidden;
        }

        .stat-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
          transition: left 0.6s ease;
        }

        .stat-card.active::before {
          left: 100%;
        }

        .stat-card.active {
          transform: scale(1.05);
          border-color: #4f46e5;
          box-shadow: 0 20px 40px rgba(79, 70, 229, 0.3);
        }

        .stat-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          border-radius: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }

        .stat-content {
          flex: 1;
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 800;
          color: white;
          margin-bottom: 0.5rem;
        }

        .stat-label {
          color: #a5b4fc;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          font-size: 0.9rem;
        }

        .stat-pulse {
          position: absolute;
          top: 50%;
          right: 2rem;
          width: 12px;
          height: 12px;
          background: #4f46e5;
          border-radius: 50%;
          transform: translateY(-50%);
          opacity: 0;
        }

        .stat-card.active .stat-pulse {
          opacity: 1;
          animation: pulse 2s ease-in-out infinite;
        }

        .story-section {
          margin-bottom: 6rem;
        }

        .story-content {
          display: grid;
          grid-template-columns: 1fr;
          gap: 4rem;
          align-items: center;
        }

        @media (min-width: 1024px) {
          .story-content {
            grid-template-columns: 1.2fr 0.8fr;
          }
        }

        .story-title {
          display: flex;
          align-items: center;
          gap: 1rem;
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          margin-bottom: 2rem;
        }

        .inline-icon {
          color: #4f46e5;
          filter: drop-shadow(0 0 10px rgba(79, 70, 229, 0.5));
        }

        .story-paragraphs p {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #cbd5e1;
          margin-bottom: 1.5rem;
        }

        .innovation-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          margin-top: 3rem;
          padding: 2rem;
          background: rgba(79, 70, 229, 0.1);
          border-radius: 1rem;
          border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .metric-item {
          text-align: center;
        }

        .metric-value {
          font-size: 2rem;
          font-weight: 800;
          color: #4f46e5;
          margin-bottom: 0.5rem;
        }

        .metric-label {
          color: #a5b4fc;
          font-size: 0.9rem;
          font-weight: 500;
        }

        .story-visual {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 500px;
        }

        .neural-network {
          position: relative;
          width: 400px;
          height: 400px;
        }

        .network-core {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100px;
          height: 100px;
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }

        .core-pulse {
          position: absolute;
          width: 120px;
          height: 120px;
          border: 2px solid #4f46e5;
          border-radius: 50%;
          animation: corePulse 3s ease-in-out infinite;
        }

        .core-icon {
          color: white;
          z-index: 1;
        }

        .network-nodes {
          position: absolute;
          width: 100%;
          height: 100%;
        }

        .network-node {
          position: absolute;
          width: 40px;
          height: 40px;
          background: rgba(79, 70, 229, 0.8);
          border-radius: 50%;
          border: 2px solid #4f46e5;
        }

        .node-pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          border: 1px solid #4f46e5;
          border-radius: 50%;
          animation: nodePulse 2s ease-in-out infinite;
        }

        .node-1 { top: 10%; left: 50%; transform: translateX(-50%); }
        .node-2 { top: 25%; right: 15%; }
        .node-3 { top: 50%; right: 5%; transform: translateY(-50%); }
        .node-4 { bottom: 25%; right: 15%; }
        .node-5 { bottom: 10%; left: 50%; transform: translateX(-50%); }
        .node-6 { bottom: 25%; left: 15%; }
        .node-7 { top: 50%; left: 5%; transform: translateY(-50%); }
        .node-8 { top: 25%; left: 15%; }

        .network-connections {
          position: absolute;
          width: 100%;
          height: 100%;
        }

        .connection {
          position: absolute;
          height: 2px;
          background: linear-gradient(90deg, #4f46e5, transparent, #4f46e5);
          transform-origin: left center;
          opacity: 0;
          animation: connectionPulse 4s ease-in-out infinite;
        }

        .connection-1 { top: 20%; left: 50%; width: 100px; transform: rotate(45deg); }
        .connection-2 { top: 35%; right: 30%; width: 80px; transform: rotate(-45deg); }
        .connection-3 { top: 50%; right: 20%; width: 120px; transform: rotate(90deg); }
        .connection-4 { bottom: 35%; right: 30%; width: 80px; transform: rotate(45deg); }
        .connection-5 { bottom: 20%; left: 50%; width: 100px; transform: rotate(-45deg); }
        .connection-6 { bottom: 35%; left: 30%; width: 80px; transform: rotate(-45deg); }
        .connection-7 { top: 50%; left: 20%; width: 120px; transform: rotate(-90deg); }
        .connection-8 { top: 35%; left: 30%; width: 80px; transform: rotate(45deg); }

        .team-section {
          margin-bottom: 6rem;
        }

        .team-title {
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          text-align: center;
          margin-bottom: 3rem;
        }

        .team-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .team-card {
          position: relative;
          padding: 2.5rem;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 1.5rem;
          transition: all 0.4s ease;
          overflow: hidden;
        }

        .team-card:hover {
          transform: translateY(-10px);
          border-color: #4f46e5;
          box-shadow: 0 25px 50px rgba(79, 70, 229, 0.3);
        }

        .team-glow {
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
          transform: rotate(45deg);
          transition: all 0.4s ease;
          opacity: 0;
        }

        .team-card:hover .team-glow {
          opacity: 1;
        }

        .team-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1.5rem;
          position: relative;
          z-index: 1;
        }

        .avatar-initial {
          font-size: 2rem;
          font-weight: 800;
          color: white;
        }

        .team-info {
          position: relative;
          z-index: 1;
        }

        .team-name {
          font-size: 1.5rem;
          font-weight: 700;
          color: white;
          margin-bottom: 0.5rem;
        }

        .team-role {
          color: #4f46e5;
          font-weight: 600;
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }

        .team-background {
          color: #cbd5e1;
          line-height: 1.6;
          font-size: 0.95rem;
        }

        .mission-statement {
          text-align: center;
          max-width: 900px;
          margin: 0 auto;
          padding: 4rem;
          background: rgba(79, 70, 229, 0.1);
          border-radius: 2rem;
          border: 1px solid rgba(79, 70, 229, 0.2);
          position: relative;
          overflow: hidden;
        }

        .mission-statement::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.05), transparent);
          animation: shimmer 3s ease-in-out infinite;
        }

        .mission-content {
          position: relative;
          z-index: 1;
        }

        .mission-title {
          font-size: 2rem;
          font-weight: 700;
          color: white;
          margin-bottom: 2rem;
        }

        .mission-text {
          font-size: 1.3rem;
          line-height: 1.8;
          color: #e2e8f0;
          font-style: italic;
          margin-bottom: 2rem;
        }

        .mission-signature {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .signature-line {
          width: 60px;
          height: 1px;
          background: #4f46e5;
        }

        .signature-text {
          color: #4f46e5;
          font-weight: 600;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
          50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
        }

        @keyframes corePulse {
          0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
          50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
        }
        @keyframes nodePulse {
          0%, 100% { transform: scale(1); opacity: 0.8; }
          50% { transform: scale(1.2); opacity: 0.4; }
        }
        @keyframes connectionPulse {
          0%, 100% { opacity: 0; }
          50% { opacity: 1; }
              `}</style>
            </div>
          );
        };
        
        export default Landing;