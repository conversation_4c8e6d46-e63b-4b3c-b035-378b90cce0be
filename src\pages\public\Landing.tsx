import React, { useState, useEffect } from 'react';
import {
  ShieldCheck,
  Lock,
  BarChart2,
  ArrowRight,
  Users,
  Globe,
  Award,
  Zap,
  Brain,
  Target
} from 'lucide-react';

const Landing = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentStat, setCurrentStat] = useState(0);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStat((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { number: '2.5M+', label: 'Students Protected', icon: Users },
    { number: '850+', label: 'Global Institutions', icon: Globe },
    { number: '99.97%', label: 'Accuracy Rate', icon: Target },
    { number: '24/7', label: 'AI Monitoring', icon: Brain }
  ];

  const teamMembers = [
    {
      name: 'Dr. Sarah Chen',
      role: 'Chief AI Architect',
      background: 'Former MIT researcher specializing in computer vision and behavioral analysis.',
      gradient: 'from-blue-500 to-purple-600'
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Security Director',
      background: 'Ex-NSA cybersecurity expert with 15 years in digital forensics.',
      gradient: 'from-green-500 to-teal-600'
    },
    {
      name: 'Dr. Priya Patel',
      role: 'Education Technology Lead',
      background: 'Stanford PhD in Educational Psychology, pioneer in adaptive learning.',
      gradient: 'from-pink-500 to-rose-600'
    },
    {
      name: 'James Thompson',
      role: 'Platform Engineering',
      background: 'Former Google engineer, architect of scalable examination systems.',
      gradient: 'from-orange-500 to-red-600'
    }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* Hero Section */}
      <div className="flex-1 flex items-center justify-center p-8 bg-gradient-to-br from-indigo-100 to-indigo-200">
        <div className="max-w-3xl text-center p-8">
          <div className="mx-auto mb-6 w-20 h-20 flex items-center justify-center bg-white rounded-full shadow-lg text-primary-600">
            <ShieldCheck size={48} />
          </div>
          <h1 className="text-4xl font-extrabold text-primary-700 mb-4 leading-tight">Welcome to ExamProctor</h1>
          <p className="text-xl text-primary-800 mb-8 max-w-2xl mx-auto">Secure, smart, and scalable online exams</p>

          <div className="flex gap-4 justify-center mt-8">
            <a href="/signup" className="flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg font-semibold no-underline transition-all duration-200 shadow-md hover:bg-primary-700 hover:-translate-y-0.5 hover:shadow-lg">
              Get Started
              <ArrowRight size={18} />
            </a>
            <a href="/login" className="px-6 py-3 bg-white text-primary-600 rounded-lg font-semibold no-underline transition-all duration-200 shadow-md border border-primary-200 hover:bg-primary-50 hover:-translate-y-0.5">
              Login
            </a>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 px-8 bg-gray-50">
        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <ShieldCheck size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Secure Proctoring</h3>
            <p className="text-gray-600 leading-relaxed">
              Advanced AI monitoring to ensure exam integrity and prevent cheating.
            </p>
          </div>

          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <Lock size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Data Protection</h3>
            <p className="text-gray-600 leading-relaxed">
              End-to-end encryption and secure storage for all exam data.
            </p>
          </div>

          <div className="bg-white p-8 rounded-xl shadow-md border border-gray-100 text-center transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
            <div className="w-16 h-16 mx-auto mb-6 flex items-center justify-center bg-primary-100 rounded-full text-primary-600">
              <BarChart2 size={32} />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">Detailed Analytics</h3>
            <p className="text-gray-600 leading-relaxed">
              Comprehensive reports and performance insights for students and instructors.
            </p>
          </div>
        </div>
      </div>

      {/* About Section */}
      <div className="relative py-20 px-8 bg-slate-900 overflow-hidden">
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(79, 70, 229, 0.15) 0%, transparent 50%)`
          }}
        />

        <div className="relative max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-6">
              <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-20"></div>
              <Award className="mx-4 text-primary-500" size={24} />
              <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-20"></div>
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Pioneering the Future of
              <span className="bg-gradient-to-r from-primary-400 to-purple-400 bg-clip-text text-transparent"> Secure Education</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Where cutting-edge AI meets uncompromising academic integrity
            </p>
          </div>

          {/* Stats Grid */}
          <div className="mb-16">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div
                    key={index}
                    className={`relative p-6 bg-slate-800 rounded-xl border transition-all duration-300 ${
                      currentStat === index
                        ? 'border-primary-500 bg-slate-700 shadow-lg shadow-primary-500/20'
                        : 'border-slate-700 hover:border-slate-600'
                    }`}
                  >
                    <div className="flex items-center justify-center w-12 h-12 mb-4 bg-primary-600 rounded-lg text-white">
                      <IconComponent size={28} />
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                      <div className="text-slate-400 text-sm">{stat.label}</div>
                    </div>
                    {currentStat === index && (
                      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary-500 to-purple-500 rounded-xl opacity-20 animate-pulse"></div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Story Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="flex items-center text-2xl font-bold text-white mb-6">
                <Zap className="mr-3 text-primary-500 drop-shadow-lg" size={32} />
                Born from Academic Excellence
              </h3>
              <div className="space-y-6">
                <p className="text-lg leading-relaxed text-slate-300">
                  In 2019, when traditional education faced its greatest disruption, a team of visionary
                  researchers from MIT, Stanford, and Oxford united with a singular mission: to preserve
                  the sanctity of academic achievement in the digital age.
                </p>
                <p className="text-lg leading-relaxed text-slate-300">
                  What began as an emergency response to remote learning challenges has evolved into
                  the world's most sophisticated proctoring ecosystem. Our neural networks don't just
                  detect cheating—they understand intent, context, and behavior patterns with
                  unprecedented precision.
                </p>
                <p className="text-lg leading-relaxed text-slate-300">
                  Today, ExamProctor stands as the guardian of academic integrity for over 850
                  institutions worldwide, protecting the value of every degree, every certification,
                  and every achievement earned through genuine effort.
                </p>
              </div>

              <div className="grid grid-cols-3 gap-8 mt-12 p-8 bg-primary-600/10 rounded-2xl border border-primary-500/20">
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">47</div>
                  <div className="text-primary-300 text-sm font-medium">AI Patents</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">0.03%</div>
                  <div className="text-primary-300 text-sm font-medium">False Positive Rate</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-extrabold text-primary-500 mb-2">&lt; 50ms</div>
                  <div className="text-primary-300 text-sm font-medium">Detection Latency</div>
                </div>
              </div>
            </div>

            {/* Neural Network Visualization */}
            <div className="flex justify-center items-center min-h-96">
              <div className="relative w-80 h-80">
                {/* Central brain icon */}
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center shadow-lg">
                  <div className="absolute inset-0 bg-primary-500 rounded-full animate-ping opacity-20"></div>
                  <Brain size={40} className="text-white relative z-10" />
                </div>

                {/* Network nodes */}
                {[...Array(8)].map((_, i) => {
                  const angle = (i * 45) * (Math.PI / 180);
                  const radius = 120;
                  const x = Math.cos(angle) * radius;
                  const y = Math.sin(angle) * radius;

                  return (
                    <div
                      key={i}
                      className="absolute w-4 h-4 bg-primary-400 rounded-full animate-pulse"
                      style={{
                        left: `calc(50% + ${x}px - 8px)`,
                        top: `calc(50% + ${y}px - 8px)`,
                        animationDelay: `${i * 0.2}s`
                      }}
                    >
                      <div className="absolute inset-0 bg-primary-300 rounded-full animate-ping opacity-40"></div>
                    </div>
                  );
                })}

                {/* Connection lines */}
                <svg className="absolute inset-0 w-full h-full">
                  {[...Array(8)].map((_, i) => {
                    const angle = (i * 45) * (Math.PI / 180);
                    const radius = 120;
                    const x = Math.cos(angle) * radius + 160;
                    const y = Math.sin(angle) * radius + 160;

                    return (
                      <line
                        key={i}
                        x1="160"
                        y1="160"
                        x2={x}
                        y2={y}
                        stroke="rgba(79, 70, 229, 0.3)"
                        strokeWidth="1"
                        className="animate-pulse"
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    );
                  })}
                </svg>
              </div>
            </div>

          {/* Team Section */}
          <div className="mt-20">
            <h3 className="text-2xl font-bold text-white text-center mb-12">Meet the Visionaries</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {teamMembers.map((member, index) => (
                <div key={index} className="relative group">
                  <div className="bg-slate-800 p-6 rounded-xl border border-slate-700 transition-all duration-300 hover:border-primary-500 hover:shadow-lg hover:shadow-primary-500/20">
                    <div className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br ${member.gradient} flex items-center justify-center text-white text-xl font-bold`}>
                      {member.name.charAt(0)}
                    </div>
                    <div className="text-center">
                      <h4 className="text-lg font-semibold text-white mb-1">{member.name}</h4>
                      <div className="text-primary-400 text-sm font-medium mb-3">{member.role}</div>
                      <p className="text-slate-400 text-sm leading-relaxed">{member.background}</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-purple-500/10 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Mission Statement */}
          <div className="mt-20 text-center">
            <div className="max-w-4xl mx-auto p-8 bg-slate-800/50 rounded-2xl border border-slate-700">
              <h3 className="text-2xl font-bold text-white mb-6">Our Unwavering Commitment</h3>
              <p className="text-lg text-slate-300 leading-relaxed mb-6 italic">
                "Every test taken, every assignment submitted, every achievement earned through
                ExamProctor represents genuine human potential unleashed. We don't just prevent
                cheating—we preserve the fundamental trust that makes education meaningful."
              </p>
              <div className="flex items-center justify-center">
                <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-32"></div>
                <span className="mx-4 text-primary-400 font-medium">The ExamProctor Team</span>
                <div className="h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent flex-1 max-w-32"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Landing;