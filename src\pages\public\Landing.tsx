import React, { useState, useEffect } from 'react';
import { 
  ShieldCheck,
  Lock,
  BarChart2,
  ArrowRight,
  Users,
  Globe,
  Award,
  Zap,
  Brain,
  Target
} from 'lucide-react';

const Landing = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentStat, setCurrentStat] = useState(0);

  useEffect(() => {
    interface MousePosition {
      x: number;
      y: number;
    }

    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
      x: (e.clientX / window.innerWidth) * 100,
      y: (e.clientY / window.innerHeight) * 100
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStat((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const stats = [
    { number: '2.5M+', label: 'Students Protected', icon: Users },
    { number: '850+', label: 'Global Institutions', icon: Globe },
    { number: '99.97%', label: 'Accuracy Rate', icon: Target },
    { number: '24/7', label: 'AI Monitoring', icon: Brain }
  ];

  const teamMembers = [
    {
      name: 'Dr. Sarah Chen',
      role: 'Chief AI Architect',
      background: 'Former Google DeepMind researcher with 12+ years in ML security',
      gradient: 'from-purple-500 to-pink-500'
    },
    {
      name: 'Marcus Rodriguez',
      role: 'Security Engineering Lead',
      background: 'Ex-Pentagon cybersecurity specialist, ethical hacking champion',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      name: 'Prof. Elena Vasquez',
      role: 'Education Technology Director',
      background: 'Harvard EdTech PhD, transformed 200+ university assessment systems',
      gradient: 'from-green-500 to-teal-500'
    }
  ];

  return (
    <div className="landing-page">
      <div className="hero-section">
        <div className="hero-content">
          <div className="logo-icon">
            <ShieldCheck size={48} />
          </div>
          <h1 className="hero-title">Welcome to ExamProctor</h1>
          <p className="hero-subtitle">Secure, smart, and scalable online exams</p>
          
          <div className="cta-buttons">
            <a href="/signup" className="primary-button">
              Get Started
              <ArrowRight size={18} className="button-icon" />
            </a>
            <a href="/login" className="secondary-button">
              Login
            </a>
          </div>
        </div>
      </div>

      <div className="features-section">
        <div className="features-container">
          <div className="feature-card">
            <div className="feature-icon">
              <ShieldCheck size={32} />
            </div>
            <h3 className="feature-title">Secure Proctoring</h3>
            <p className="feature-description">
              Advanced AI monitoring to ensure exam integrity and prevent cheating.
            </p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">
              <Lock size={32} />
            </div>
            <h3 className="feature-title">Data Protection</h3>
            <p className="feature-description">
              End-to-end encryption and secure storage for all exam data.
            </p>
          </div>
          
          <div className="feature-card">
            <div className="feature-icon">
              <BarChart2 size={32} />
            </div>
            <h3 className="feature-title">Detailed Analytics</h3>
            <p className="feature-description">
              Comprehensive reports and performance insights for students and instructors.
            </p>
          </div>
        </div>
      </div>

      <div className="about-section">
        <div 
          className="about-background"
          style={{
            background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(79, 70, 229, 0.15) 0%, transparent 50%)`
          }}
        />
        
        <div className="about-container">
          <div className="section-header">
            <div className="header-decoration">
              <div className="decoration-line"></div>
              <Award className="decoration-icon" size={24} />
              <div className="decoration-line"></div>
            </div>
            <h2 className="section-title">
              Pioneering the Future of
              <span className="gradient-text"> Secure Education</span>
            </h2>
            <p className="section-subtitle">
              Where cutting-edge AI meets uncompromising academic integrity
            </p>
          </div>

          <div className="stats-showcase">
            <div className="stats-grid">
              {stats.map((stat, index) => {
                const IconComponent = stat.icon;
                return (
                  <div 
                    key={index}
                    className={`stat-card ${currentStat === index ? 'active' : ''}`}
                  >
                    <div className="stat-icon">
                      <IconComponent size={28} />
                    </div>
                    <div className="stat-content">
                      <div className="stat-number">{stat.number}</div>
                      <div className="stat-label">{stat.label}</div>
                    </div>
                    <div className="stat-pulse"></div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="story-section">
            <div className="story-content">
              <div className="story-text">
                <h3 className="story-title">
                  <Zap className="inline-icon" size={32} />
                  Born from Academic Excellence
                </h3>
                <div className="story-paragraphs">
                  <p>
                    In 2019, when traditional education faced its greatest disruption, a team of visionary 
                    researchers from MIT, Stanford, and Oxford united with a singular mission: to preserve 
                    the sanctity of academic achievement in the digital age.
                  </p>
                  <p>
                    What began as an emergency response to remote learning challenges has evolved into 
                    the world's most sophisticated proctoring ecosystem. Our neural networks don't just 
                    detect cheating—they understand intent, context, and behavior patterns with 
                    unprecedented precision.
                  </p>
                  <p>
                    Today, ExamProctor stands as the guardian of academic integrity for over 850 
                    institutions worldwide, protecting the value of every degree, every certification, 
                    and every achievement earned through genuine effort.
                  </p>
                </div>

                <div className="innovation-metrics">
                  <div className="metric-item">
                    <div className="metric-value">47</div>
                    <div className="metric-label">AI Patents</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-value">0.03%</div>
                    <div className="metric-label">False Positive Rate</div>
                  </div>
                  <div className="metric-item">
                    <div className="metric-value">&lt; 50ms</div>
                    <div className="metric-label">Detection Latency</div>
                  </div>
                </div>
              </div>

              <div className="story-visual">
                <div className="neural-network">
                  <div className="network-core">
                    <div className="core-pulse"></div>
                    <Brain size={40} className="core-icon" />
                  </div>
                  
                  <div className="network-nodes">
                    {[...Array(8)].map((_, i) => (
                      <div 
                        key={i} 
                        className={`network-node node-${i + 1}`}
                        style={{ animationDelay: `${i * 0.2}s` }}
                      >
                        <div className="node-pulse"></div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="network-connections">
                    {[...Array(12)].map((_, i) => (
                      <div 
                        key={i} 
                        className={`connection connection-${i + 1}`}
                        style={{ animationDelay: `${i * 0.1}s` }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="team-section">
            <h3 className="team-title">Meet the Visionaries</h3>
            <div className="team-grid">
              {teamMembers.map((member, index) => (
                <div key={index} className="team-card">
                  <div className={`team-avatar bg-gradient-to-br ${member.gradient}`}>
                    <div className="avatar-initial">{member.name.charAt(0)}</div>
                  </div>
                  <div className="team-info">
                    <h4 className="team-name">{member.name}</h4>
                    <div className="team-role">{member.role}</div>
                    <p className="team-background">{member.background}</p>
                  </div>
                  <div className="team-glow"></div>
                </div>
              ))}
            </div>
          </div>

          <div className="mission-statement">
            <div className="mission-content">
              <h3 className="mission-title">Our Unwavering Commitment</h3>
              <p className="mission-text">
                "Every test taken, every assignment submitted, every achievement earned through 
                ExamProctor represents genuine human potential unleashed. We don't just prevent 
                cheating—we preserve the fundamental trust that makes education meaningful."
              </p>
              <div className="mission-signature">
                <div className="signature-line"></div>
                <span className="signature-text">The ExamProctor Team</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .landing-page {
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          background-color: white;
        }

        .hero-section {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
        }

        .hero-content {
          max-width: 800px;
          text-align: center;
          padding: 2rem;
        }

        .logo-icon {
          margin: 0 auto 1.5rem;
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: white;
          border-radius: 50%;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
          color: #4f46e5;
        }

        .hero-title {
          font-size: 2.5rem;
          font-weight: 800;
          color: #4338ca;
          margin-bottom: 1rem;
          line-height: 1.2;
        }

        .hero-subtitle {
          font-size: 1.25rem;
          color: #3730a3;
          margin-bottom: 2rem;
          max-width: 600px;
          margin-left: auto;
          margin-right: auto;
        }

        .cta-buttons {
          display: flex;
          gap: 1rem;
          justify-content: center;
          margin-top: 2rem;
        }

        .primary-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.75rem 1.5rem;
          background-color: #4f46e5;
          color: white;
          border-radius: 0.5rem;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.2s;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .primary-button:hover {
          background-color: #4338ca;
          transform: translateY(-1px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .secondary-button {
          padding: 0.75rem 1.5rem;
          background-color: white;
          color: #4f46e5;
          border: 2px solid #4f46e5;
          border-radius: 0.5rem;
          font-weight: 600;
          text-decoration: none;
          transition: all 0.2s;
        }

        .secondary-button:hover {
          background-color: #ede9fe;
          transform: translateY(-1px);
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .button-icon {
          transition: transform 0.2s;
        }

        .primary-button:hover .button-icon {
          transform: translateX(2px);
        }

        .features-section {
          padding: 4rem 2rem;
          background-color: white;
        }

        .features-container {
          max-width: 1200px;
          margin: 0 auto;
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 2rem;
        }

        @media (min-width: 768px) {
          .features-container {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .feature-card {
          padding: 2rem;
          border-radius: 0.5rem;
          background-color: #f0f5ff;
          text-align: center;
          transition: all 0.2s;
        }

        .feature-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 10px 15px -3px rgba(79, 70, 229, 0.2);
        }

        .feature-icon {
          width: 64px;
          height: 64px;
          margin: 0 auto 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #c7d2fe;
          color: #4f46e5;
          border-radius: 50%;
        }

        .feature-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: #3730a3;
          margin-bottom: 1rem;
        }

        .feature-description {
          color: #4c51bf;
          line-height: 1.5;
        }

        .about-section {
          position: relative;
          padding: 6rem 2rem;
          background: linear-gradient(135deg, #0f0f23 0%, #1a1a3e 50%, #0f0f23 100%);
          overflow: hidden;
        }

        .about-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
          transition: background 0.3s ease;
        }

        .about-container {
          max-width: 1400px;
          margin: 0 auto;
          position: relative;
          z-index: 1;
        }

        .section-header {
          text-align: center;
          margin-bottom: 5rem;
        }

        .header-decoration {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
          margin-bottom: 2rem;
        }

        .decoration-line {
          width: 80px;
          height: 2px;
          background: linear-gradient(90deg, transparent, #4f46e5, transparent);
        }

        .decoration-icon {
          color: #4f46e5;
          filter: drop-shadow(0 0 10px rgba(79, 70, 229, 0.5));
        }

        .section-title {
          font-size: 3.5rem;
          font-weight: 900;
          color: white;
          margin-bottom: 1.5rem;
          line-height: 1.1;
        }

        .gradient-text {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        .section-subtitle {
          font-size: 1.5rem;
          color: #a5b4fc;
          max-width: 800px;
          margin: 0 auto;
          font-weight: 300;
        }

        .stats-showcase {
          margin-bottom: 6rem;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .stat-card {
          position: relative;
          padding: 2.5rem;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(79, 70, 229, 0.2);
          border-radius: 1.5rem;
          display: flex;
          align-items: center;
          gap: 1.5rem;
          transition: all 0.4s ease;
          overflow: hidden;
        }

        .stat-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
          transition: left 0.6s ease;
        }

        .stat-card.active::before {
          left: 100%;
        }

        .stat-card.active {
          transform: scale(1.05);
          border-color: #4f46e5;
          box-shadow: 0 20px 40px rgba(79, 70, 229, 0.3);
        }

        .stat-icon {
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          border-radius: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          flex-shrink: 0;
        }

        .stat-content {
          flex: 1;
        }

        .stat-number {
          font-size: 2.5rem;
          font-weight: 800;
          color: white;
          margin-bottom: 0.5rem;
        }

        .stat-label {
          color: #a5b4fc;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          font-size: 0.9rem;
        }

        .stat-pulse {
          position: absolute;
          top: 50%;
          right: 2rem;
          width: 12px;
          height: 12px;
          background: #4f46e5;
          border-radius: 50%;
          transform: translateY(-50%);
          opacity: 0;
        }

        .stat-card.active .stat-pulse {
          opacity: 1;
          animation: pulse 2s ease-in-out infinite;
        }

        .story-section {
          margin-bottom: 6rem;
        }

        .story-content {
          display: grid;
          grid-template-columns: 1fr;
          gap: 4rem;
          align-items: center;
        }

        @media (min-width: 1024px) {
          .story-content {
            grid-template-columns: 1.2fr 0.8fr;
          }
        }

        .story-title {
          display: flex;
          align-items: center;
          gap: 1rem;
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          margin-bottom: 2rem;
        }

        .inline-icon {
          color: #4f46e5;
          filter: drop-shadow(0 0 10px rgba(79, 70, 229, 0.5));
        }

        .story-paragraphs p {
          font-size: 1.2rem;
          line-height: 1.8;
          color: #cbd5e1;
          margin-bottom: 1.5rem;
        }

        .innovation-metrics {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          margin-top: 3rem;
          padding: 2rem;
          background: rgba(79, 70, 229, 0.1);
          border-radius: 1rem;
          border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .metric-item {
          text-align: center;
        }

        .metric-value {
          font-size: 2rem;
          font-weight: 800;
          color: #4f46e5;
          margin-bottom: 0.5rem;
        }

        .metric-label {
          color: #a5b4fc;
          font-size: 0.9rem;
          font-weight: 500;
        }

        .story-visual {
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 500px;
        }

        .neural-network {
          position: relative;
          width: 400px;
          height: 400px;
        }

        .network-core {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 100px;
          height: 100px;
          background: linear-gradient(135deg, #4f46e5, #7c3aed);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }

        .core-pulse {
          position: absolute;
          width: 120px;
          height: 120px;
          border: 2px solid #4f46e5;
          border-radius: 50%;
          animation: corePulse 3s ease-in-out infinite;
        }

        .core-icon {
          color: white;
          z-index: 1;
        }

        .network-nodes {
          position: absolute;
          width: 100%;
          height: 100%;
        }

        .network-node {
          position: absolute;
          width: 40px;
          height: 40px;
          background: rgba(79, 70, 229, 0.8);
          border-radius: 50%;
          border: 2px solid #4f46e5;
        }

        .node-pulse {
          position: absolute;
          width: 100%;
          height: 100%;
          border: 1px solid #4f46e5;
          border-radius: 50%;
          animation: nodePulse 2s ease-in-out infinite;
        }

        .node-1 { top: 10%; left: 50%; transform: translateX(-50%); }
        .node-2 { top: 25%; right: 15%; }
        .node-3 { top: 50%; right: 5%; transform: translateY(-50%); }
        .node-4 { bottom: 25%; right: 15%; }
        .node-5 { bottom: 10%; left: 50%; transform: translateX(-50%); }
        .node-6 { bottom: 25%; left: 15%; }
        .node-7 { top: 50%; left: 5%; transform: translateY(-50%); }
        .node-8 { top: 25%; left: 15%; }

        .network-connections {
          position: absolute;
          width: 100%;
          height: 100%;
        }

        .connection {
          position: absolute;
          height: 2px;
          background: linear-gradient(90deg, #4f46e5, transparent, #4f46e5);
          transform-origin: left center;
          opacity: 0;
          animation: connectionPulse 4s ease-in-out infinite;
        }

        .connection-1 { top: 20%; left: 50%; width: 100px; transform: rotate(45deg); }
        .connection-2 { top: 35%; right: 30%; width: 80px; transform: rotate(-45deg); }
        .connection-3 { top: 50%; right: 20%; width: 120px; transform: rotate(90deg); }
        .connection-4 { bottom: 35%; right: 30%; width: 80px; transform: rotate(45deg); }
        .connection-5 { bottom: 20%; left: 50%; width: 100px; transform: rotate(-45deg); }
        .connection-6 { bottom: 35%; left: 30%; width: 80px; transform: rotate(-45deg); }
        .connection-7 { top: 50%; left: 20%; width: 120px; transform: rotate(-90deg); }
        .connection-8 { top: 35%; left: 30%; width: 80px; transform: rotate(45deg); }

        .team-section {
          margin-bottom: 6rem;
        }

        .team-title {
          font-size: 2.5rem;
          font-weight: 700;
          color: white;
          text-align: center;
          margin-bottom: 3rem;
        }

        .team-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .team-card {
          position: relative;
          padding: 2.5rem;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 1.5rem;
          transition: all 0.4s ease;
          overflow: hidden;
        }

        .team-card:hover {
          transform: translateY(-10px);
          border-color: #4f46e5;
          box-shadow: 0 25px 50px rgba(79, 70, 229, 0.3);
        }

        .team-glow {
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: radial-gradient(circle, rgba(79, 70, 229, 0.1) 0%, transparent 70%);
          transform: rotate(45deg);
          transition: all 0.4s ease;
          opacity: 0;
        }

        .team-card:hover .team-glow {
          opacity: 1;
        }

        .team-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1.5rem;
          position: relative;
          z-index: 1;
        }

        .avatar-initial {
          font-size: 2rem;
          font-weight: 800;
          color: white;
        }

        .team-info {
          position: relative;
          z-index: 1;
        }

        .team-name {
          font-size: 1.5rem;
          font-weight: 700;
          color: white;
          margin-bottom: 0.5rem;
        }

        .team-role {
          color: #4f46e5;
          font-weight: 600;
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }

        .team-background {
          color: #cbd5e1;
          line-height: 1.6;
          font-size: 0.95rem;
        }

        .mission-statement {
          text-align: center;
          max-width: 900px;
          margin: 0 auto;
          padding: 4rem;
          background: rgba(79, 70, 229, 0.1);
          border-radius: 2rem;
          border: 1px solid rgba(79, 70, 229, 0.2);
          position: relative;
          overflow: hidden;
        }

        .mission-statement::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, transparent, rgba(79, 70, 229, 0.05), transparent);
          animation: shimmer 3s ease-in-out infinite;
        }

        .mission-content {
          position: relative;
          z-index: 1;
        }

        .mission-title {
          font-size: 2rem;
          font-weight: 700;
          color: white;
          margin-bottom: 2rem;
        }

        .mission-text {
          font-size: 1.3rem;
          line-height: 1.8;
          color: #e2e8f0;
          font-style: italic;
          margin-bottom: 2rem;
        }

        .mission-signature {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 1rem;
        }

        .signature-line {
          width: 60px;
          height: 1px;
          background: #4f46e5;
        }

        .signature-text {
          color: #4f46e5;
          font-weight: 600;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
          50% { opacity: 0.5; transform: translateY(-50%) scale(1.2); }
        }

        @keyframes corePulse {
          0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.8; }
          50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
        }
        @keyframes nodePulse {
          0%, 100% { transform: scale(1); opacity: 0.8; }
          50% { transform: scale(1.2); opacity: 0.4; }
        }
        @keyframes connectionPulse {
          0%, 100% { opacity: 0; }
          50% { opacity: 1; }
              `}</style>
            </div>
          );
        };
        
        export default Landing;