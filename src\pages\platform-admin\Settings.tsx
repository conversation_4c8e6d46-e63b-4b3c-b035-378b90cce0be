const PlatformAdminSettings = () => {
  return (
    <div>
      <h1 className="text-2xl font-bold text-indigo-700 mb-4">Platform System Settings</h1>
      <div className="bg-white p-4 rounded shadow space-y-4 max-w-md">
        <div>
          <label className="block font-semibold mb-1">Global Payment Gateway API Key</label>
          <input type="text" className="w-full border px-3 py-2 rounded" placeholder="••••••••••••••••••••••••" />
        </div>
        <div>
          <label className="block font-semibold mb-1">Default Data Retention (Platform-wide)</label>
          <input type="number" className="w-full border px-3 py-2 rounded" placeholder="365" />
        </div>
        <div>
          <label className="block font-semibold mb-1">Platform Branding Logo URL</label>
          <input type="url" className="w-full border px-3 py-2 rounded" placeholder="https://your-logo.com/logo.png" />
        </div>
        <button className="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700">Save Global Settings</button>
      </div>
    </div>
  );
};

export default PlatformAdminSettings;