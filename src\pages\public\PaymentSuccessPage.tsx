import { useState, useEffect } from 'react';
import { CheckCircleIcon, DocumentDuplicateIcon } from '@heroicons/react/24/outline';

interface PaymentSuccessPageProps {
  selectedPlan: {
    id: string;
    name: string;
    price: number;
    billing: string;
    features: string[];
    isPopular: boolean;
    savings: number | null;
  };
  finalPrice: string;
}

const PaymentSuccessPage: React.FC<PaymentSuccessPageProps> = ({ selectedPlan, finalPrice }) => {
  const [accessCode, setAccessCode] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState('');

  // Function to generate a random access code
  const generateAccessCode = () => {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 10; i++) { // Generate a 10-character code
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  useEffect(() => {
    // Generate access code when the component mounts
    setAccessCode(generateAccessCode());
  }, []); // Empty dependency array ensures it runs only once on mount

  const copyToClipboard = async () => {
    if (accessCode) {
      try {
        await navigator.clipboard.writeText(accessCode);
        setCopySuccess('Copied!');
        setTimeout(() => setCopySuccess(''), 2000); // Clear message after 2 seconds
      } catch (err) {
        setCopySuccess('Failed to copy!');
        console.error('Failed to copy text: ', err);
      }
    }
  };

  return (
    <div className="payment-success-container">
      <div className="payment-success-content">
        <div className="success-icon">
          <CheckCircleIcon />
        </div>
        <h1>Payment Successful!</h1>
        <p className="success-message">Thank you for subscribing to **{selectedPlan.name}**. Your account has been upgraded.</p>

        <div className="access-code-section">
          <p className="access-code-prompt">Your unique access code for profile setup:</p>
          <div className="access-code-display">
            <span className="access-code">{accessCode}</span>
            <button className="copy-button" onClick={copyToClipboard}>
              <DocumentDuplicateIcon className="icon" />
              {copySuccess || 'Copy Code'}
            </button>
          </div>
          <p className="access-code-note">Please save this code. You will need it to verify your subscription during the profile setup process. **Do not share this code.**</p>
        </div>

        <div className="order-summary-success">
          <h2>Order Summary</h2>
          <div className="summary-row">
            <span>Plan:</span>
            <span>{selectedPlan.name}</span>
          </div>
          <div className="summary-row">
            <span>Amount Paid:</span>
            <span>${finalPrice}</span>
          </div>
          <div className="summary-row">
            <span>Billing Cycle:</span>
            <span>{selectedPlan.billing === 'month' ? 'Monthly' : 'Annual'}</span>
          </div>
        </div>
        <button 
          className="dashboard-button"
          onClick={() => window.location.href = '/profile-setup'}
        >
          Go to Profile Setup
        </button>
      </div>

      <style>{`
        :root {
            --primary-color: #4f46e5;
            --primary-dark: #4338ca;
            --primary-light: #eef2ff;
            --green-success: #10b981;
            --green-light: #ecfdf5;
            --text-dark: #1f2937;
            --text-medium: #4b5563;
            --text-light: #6b7280;
            --border-color: #e5e7eb;
            --bg-light: #f9fafb;
            --bg-gradient-start: #f0f4ff;
            --bg-gradient-end: #e6f0ff;
            --code-bg: #f3f4f6;
            --code-text: #374151;
        }

        .payment-success-container {
          min-height: 100vh;
          background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          font-family: 'Inter', sans-serif;
        }

        .payment-success-content {
          background: white;
          border-radius: 1rem;
          box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08);
          border: 1px solid var(--border-color);
          padding: 3.5rem 3rem;
          max-width: 650px;
          width: 100%;
          text-align: center;
          animation: slideInUp 0.7s ease-out;
        }

        .success-icon {
          width: 6rem;
          height: 6rem;
          margin: 0 auto 2rem;
          color: var(--green-success);
          animation: checkmark 0.6s ease-out forwards;
        }
        
        @keyframes checkmark {
            0% { transform: scale(0.5); opacity: 0; }
            70% { transform: scale(1.1); opacity: 1; }
            100% { transform: scale(1); }
        }

        .payment-success-content h1 {
          font-size: 2.2rem;
          font-weight: 800;
          color: var(--text-dark);
          margin-bottom: 1.2rem;
          letter-spacing: -0.02em;
        }

        .success-message {
          font-size: 1.1rem;
          color: var(--text-light);
          margin-bottom: 2.5rem;
          line-height: 1.7;
          font-weight: 500;
        }
        .success-message strong {
            color: var(--primary-dark);
        }

        /* Access Code Section Styles */
        .access-code-section {
            background: var(--code-bg);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
            padding: 1.8rem;
            margin-bottom: 2.5rem;
            text-align: center;
            box-shadow: inset 0 1px 5px rgba(0,0,0,0.03);
            animation: fadeIn 0.8s ease-out 0.3s forwards;
            opacity: 0;
        }

        .access-code-prompt {
            font-size: 1rem;
            color: var(--text-medium);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .access-code-display {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 1.2rem;
            flex-wrap: wrap;
        }

        .access-code {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--code-text);
            background: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            border: 2px dashed var(--primary-light);
            letter-spacing: 0.05em;
            user-select: text;
            flex-grow: 1;
            text-align: center;
            min-width: 200px;
            overflow-wrap: break-word;
        }

        .copy-button {
            padding: 0.8rem 1.2rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.2);
            font-size: 0.95rem;
        }

        .copy-button:hover {
            background: var(--primary-dark);
            box-shadow: 0 6px 15px rgba(79, 70, 229, 0.3);
            transform: translateY(-1px);
        }

        .copy-button .icon {
            width: 1.2rem;
            height: 1.2rem;
        }

        .access-code-note {
            font-size: 0.9rem;
            color: #ef4444;
            font-weight: 500;
            line-height: 1.5;
        }
        .access-code-note strong {
            color: #dc2626;
        }

        .order-summary-success {
          background: var(--bg-light);
          border-radius: 0.75rem;
          padding: 1.8rem;
          margin-bottom: 2.5rem;
          text-align: left;
          border: 1px solid var(--border-color);
        }

        .order-summary-success h2 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-dark);
          margin-bottom: 1.2rem;
          text-align: center;
          border-bottom: 1px dashed var(--border-color);
          padding-bottom: 0.75rem;
        }

        .order-summary-success .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.6rem;
            font-size: 0.95rem;
            color: var(--text-medium);
        }

        .order-summary-success .summary-row span:last-child {
            font-weight: 600;
            color: var(--text-dark);
        }

        .dashboard-button {
          width: 100%;
          padding: 1.1rem;
          background: var(--primary-color);
          color: white;
          border: none;
          border-radius: 0.6rem;
          font-size: 1.1rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease-in-out;
          box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
        }

        .dashboard-button:hover {
          background: var(--primary-dark);
          box-shadow: 0 10px 25px rgba(79, 70, 229, 0.4);
          transform: translateY(-2px);
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
          .payment-success-content {
            padding: 2.5rem 1.5rem;
          }

          .success-icon {
            width: 4rem;
            height: 4rem;
          }

          .payment-success-content h1 {
            font-size: 1.8rem;
          }

          .payment-success-content p {
            font-size: 0.95rem;
          }

          .access-code-display {
            flex-direction: column;
            gap: 0.75rem;
          }
          .access-code {
            font-size: 1.5rem;
            width: 100%;
            padding: 0.6rem 1rem;
          }
          .copy-button {
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
};

export default PaymentSuccessPage;
