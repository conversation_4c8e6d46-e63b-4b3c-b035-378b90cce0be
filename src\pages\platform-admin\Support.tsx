const SupportRequests = () => {
  const supportTickets = [
    { id: 1, tenant: 'University A', subject: 'Login issues for instructor', status: 'Open', lastUpdate: '2025-05-23' },
    { id: 2, tenant: 'University B', subject: 'Exam proctoring feature not working', status: 'In Progress', lastUpdate: '2025-05-22' },
    { id: 3, tenant: 'New University C', subject: 'Setup assistance required', status: 'New', lastUpdate: '2025-05-23' },
  ];

  return (
    <div>
      <h1 className="text-2xl font-bold text-indigo-700 mb-4">Support Requests</h1>
      <table className="w-full bg-white rounded shadow">
        <thead>
          <tr className="bg-gray-100 text-left">
            <th className="p-3">Ticket ID</th>
            <th className="p-3">Tenant</th>
            <th className="p-3">Subject</th>
            <th className="p-3">Status</th>
            <th className="p-3">Last Update</th>
            <th className="p-3">Actions</th>
          </tr>
        </thead>
        <tbody>
          {supportTickets.map((ticket) => (
            <tr key={ticket.id} className="border-t">
              <td className="p-3">{ticket.id}</td>
              <td className="p-3">{ticket.tenant}</td>
              <td className="p-3">{ticket.subject}</td>
              <td className="p-3">{ticket.status}</td>
              <td className="p-3">{ticket.lastUpdate}</td>
              <td className="p-3">
                <button className="text-blue-600 hover:underline text-sm">View Ticket</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default SupportRequests;