import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { Book, Calendar, CheckCircle, Clock, Award, BarChart2, Pie<PERSON>hart, Users, AlertCircle } from 'lucide-react';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('overview');
  
  // Mock data for the dashboard
  const summary = [
    { title: 'Total Exams', value: 12, icon: <Book className="h-6 w-6" /> },
    { title: 'Pending Exams', value: 4, icon: <Clock className="h-6 w-6" /> },
    { title: 'Completed Exams', value: 8, icon: <CheckCircle className="h-6 w-6" /> },
  ];
  
  const courses = [
    { id: 1, title: 'Web Development', students: 45, progress: 68 },
    { id: 2, title: 'Data Science', students: 32, progress: 42 },
    { id: 3, title: 'Mobile App Development', students: 28, progress: 75 },
  ];
  
  const exams = [
    { id: 1, title: 'JavaScript Fundamentals', date: '2025-05-25', status: 'Completed', submissions: 24, avgScore: 78 },
    { id: 2, title: 'Data Structures', date: '2025-06-02', status: 'Scheduled', submissions: 0, avgScore: 0 },
    { id: 3, title: 'Python Basics', status: 'Draft', submissions: 0, avgScore: 0 },
    { id: 4, title: 'Database Design', date: '2025-05-18', status: 'Completed', submissions: 18, avgScore: 82 },
    { id: 5, title: 'React Fundamentals', date: '2025-05-28', status: 'Scheduled', submissions: 0, avgScore: 0 },
  ];
  
  const flaggedSubmissions = [
    { id: 1, student: 'Alex Johnson', exam: 'JavaScript Fundamentals', issue: 'Potential plagiarism', date: '2025-05-19' },
    { id: 2, student: 'Morgan Smith', exam: 'Database Design', issue: 'Suspicious activity', date: '2025-05-18' },
  ];
  
  const examPerformance = [
    { name: 'JavaScript', score: 78, average: 65 },
    { name: 'Python', score: 82, average: 70 },
    { name: 'Databases', score: 75, average: 68 },
    { name: 'React', score: 88, average: 76 },
  ];
  
  // Helper function to get status color
  interface SummaryItem {
    title: string;
    value: number;
    icon: React.ReactNode;
  }

  interface Course {
    id: number;
    title: string;
    students: number;
    progress: number;
  }

  type ExamStatus = 'Completed' | 'Scheduled' | 'Draft';

  interface Exam {
    id: number;
    title: string;
    date?: string;
    status: ExamStatus;
    submissions: number;
    avgScore: number;
  }

  interface FlaggedSubmission {
    id: number;
    student: string;
    exam: string;
    issue: string;
    date: string;
  }

  interface ExamPerformance {
    name: string;
    score: number;
    average: number;
  }

  const getStatusColor = (status: ExamStatus): string => {
    switch(status) {
      case 'Completed': return 'text-green-500';
      case 'Scheduled': return 'text-blue-500';
      case 'Draft': return 'text-gray-500';
      default: return 'text-gray-700';
    }
  };
  
  // Helper function to format date
  interface FormatDate {
    (dateString?: string): string;
  }

  const formatDate: FormatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };
  
  return (
    <div className="dashboard-container">
      {/* Left sidebar would go here, matching your existing sidebar component */}
      
      <div className="dashboard-content">
        <div className="dashboard-header">
          <h1 className="dashboard-title">Instructor Dashboard</h1>
          <div className="dashboard-actions">
            <button className="action-button primary">
              <Calendar className="action-icon" /> Schedule Exam
            </button>
            <button className="action-button secondary">
              <Users className="action-icon" /> Manage Students
            </button>
          </div>
        </div>
        
        {/* Dashboard Navigation */}
        <div className="nav-container">
          <nav className="dashboard-nav">
            <button 
              onClick={() => setActiveTab('overview')} 
              className={`nav-item ${activeTab === 'overview' ? 'active' : ''}`}
            >
              Overview
            </button>
            <button 
              onClick={() => setActiveTab('courses')} 
              className={`nav-item ${activeTab === 'courses' ? 'active' : ''}`}
            >
              Courses
            </button>
            <button 
              onClick={() => setActiveTab('exams')} 
              className={`nav-item ${activeTab === 'exams' ? 'active' : ''}`}
            >
              Exams
            </button>
            <button 
              onClick={() => setActiveTab('insights')} 
              className={`nav-item ${activeTab === 'insights' ? 'active' : ''}`}
            >
              Insights
            </button>
          </nav>
        </div>
        
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <>
            {/* Summary Cards */}
            <div className="stats-grid">
              {summary.map((item, idx) => (
                <div key={idx} className="stat-card">
                  <div className="stat-header">
                    <h3 className="stat-title">{item.title}</h3>
                    <div className="stat-icon">{item.icon}</div>
                  </div>
                  <p className="stat-value">{item.value}</p>
                  <div className="stat-meta">
                    {idx === 0 && '3 new this month'}
                    {idx === 1 && '2 due this week'}
                    {idx === 2 && '4 graded recently'}
                  </div>
                </div>
              ))}
            </div>
            
            {/* Main Content Grid */}
            <div className="main-content-grid">
              {/* Recent Exams */}
              <div className="chart-card">
                <div className="card-header">
                  <h3 className="card-title">Recent Exams</h3>
                  <button className="card-action">View All</button>
                </div>
                <div className="exams-list">
                  {exams.slice(0, 3).map(exam => (
                    <div key={exam.id} className="exam-item">
                      <div className="exam-info">
                        <h4 className="exam-subject">{exam.title}</h4>
                        <p className="exam-date">
                          {exam.status === 'Completed' 
                            ? `${exam.submissions} submissions, Avg: ${exam.avgScore}%` 
                            : exam.status === 'Scheduled' 
                              ? `Scheduled for ${formatDate(exam.date)}` 
                              : 'Draft - Not yet published'}
                        </p>
                      </div>
                      <span className={`exam-status ${exam.status.toLowerCase()}`}>
                        {exam.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* Flagged Submissions */}
              <div className="chart-card">
                <div className="card-header">
                  <h3 className="card-title">Flagged Submissions</h3>
                  <button className="card-action">Review All</button>
                </div>
                {flaggedSubmissions.length > 0 ? (
                  <div className="activity-list">
                    {flaggedSubmissions.map(item => (
                      <div key={item.id} className="activity-item">
                        <div className="activity-icon alert">
                          <AlertCircle className="icon-small" />
                        </div>
                        <div className="activity-content">
                          <p className="activity-title">{item.student}</p>
                          <p className="activity-description">{item.exam} - {item.issue}</p>
                          <p className="activity-time">Flagged on {formatDate(item.date)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-state">
                    <p>No flagged submissions at this time</p>
                  </div>
                )}
              </div>
            </div>
            
            {/* Course Progress Overview */}
            <div className="activity-card">
              <div className="card-header">
                <h3 className="card-title">Course Progress Overview</h3>
                <button className="card-action">View Details</button>
              </div>
              <div className="progress-list">
                {courses.map(course => (
                  <div key={course.id} className="progress-item">
                    <div className="progress-header">
                      <span className="progress-title">{course.title}</span>
                      <span className="progress-value">{course.progress}%</span>
                    </div>
                    <div className="progress-bar-bg">
                      <div 
                        className="progress-bar-fill"
                        style={{ width: `${course.progress}%` }}
                      ></div>
                    </div>
                    <div className="progress-meta">{course.students} enrolled students</div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Performance Chart */}
            <div className="activity-card">
              <div className="card-header">
                <h3 className="card-title">Performance Overview</h3>
                <button className="card-action">See Details</button>
              </div>
              <div className="chart-container">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={examPerformance}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="score" fill="#4f46e5" name="Average Score" />
                    <Bar dataKey="average" fill="#a5b4fc" name="Class Average" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </>
        )}
        
        {/* Courses Tab */}
        {activeTab === 'courses' && (
          <div className="activity-card">
            <div className="card-header">
              <h3 className="card-title">Your Courses</h3>
              <button className="card-action primary">Add New Course</button>
            </div>
            
            <div className="courses-grid">
              {courses.map(course => (
                <div key={course.id} className="course-card">
                  <div className="course-header"></div>
                  <div className="course-content">
                    <h3 className="course-title">{course.title}</h3>
                    <div className="course-meta">
                      <Users className="icon-small" />
                      <span>{course.students} Students</span>
                    </div>
                    <div className="course-progress">
                      <div className="progress-header">
                        <span className="progress-label">Progress</span>
                        <span className="progress-value">{course.progress}%</span>
                      </div>
                      <div className="progress-bar-bg">
                        <div 
                          className="progress-bar-fill"
                          style={{ width: `${course.progress}%` }}
                        ></div>
                      </div>
                    </div>
                    <div className="course-actions">
                      <button className="course-action primary">View Details</button>
                      <button className="course-action secondary">Manage Exams</button>
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Add New Course Card */}
              <div className="course-card add-new">
                <div className="add-new-content">
                  <div className="add-new-icon">
                    <svg className="icon-medium" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </div>
                  <h3 className="add-new-title">Create a new course</h3>
                  <p className="add-new-description">Get started with a new curriculum</p>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Exams Tab */}
        {activeTab === 'exams' && (
          <div className="activity-card">
            <div className="card-header">
              <h3 className="card-title">Exam Management</h3>
              <div className="header-actions">
                <button className="card-action secondary">Filter</button>
                <button className="card-action primary">Create New Exam</button>
              </div>
            </div>
            
            <div className="table-container">
              <table className="exams-table">
                <thead>
                  <tr>
                    <th>Exam Title</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>Submissions</th>
                    <th>Avg Score</th>
                    <th className="actions-column">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {exams.map(exam => (
                    <tr key={exam.id}>
                      <td className="exam-title-cell">{exam.title}</td>
                      <td>{formatDate(exam.date)}</td>
                      <td>
                        <span className={`status-badge ${exam.status.toLowerCase()}`}>
                          {exam.status}
                        </span>
                      </td>
                      <td>{exam.submissions}</td>
                      <td>{exam.avgScore > 0 ? `${exam.avgScore}%` : '-'}</td>
                      <td className="table-actions">
                        <button className="table-action view">View</button>
                        <button className="table-action edit">Edit</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
        
        {/* Insights Tab */}
        {activeTab === 'insights' && (
          <>
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-header">
                  <h3 className="stat-title">Completion Rate</h3>
                  <div className="stat-icon"><BarChart2 className="h-6 w-6" /></div>
                </div>
                <p className="stat-value">76%</p>
                <div className="stat-meta positive">↑ 12% from last month</div>
              </div>
              
              <div className="stat-card">
                <div className="stat-header">
                  <h3 className="stat-title">Average Score</h3>
                  <div className="stat-icon"><Award className="h-6 w-6" /></div>
                </div>
                <p className="stat-value">82%</p>
                <div className="stat-meta positive">↑ 5% from last month</div>
              </div>
              
              <div className="stat-card">
                <div className="stat-header">
                  <h3 className="stat-title">Student Engagement</h3>
                  <div className="stat-icon"><Users className="h-6 w-6" /></div>
                </div>
                <p className="stat-value">89%</p>
                <div className="stat-meta negative">↓ 3% from last month</div>
              </div>
            </div>
            
            <div className="main-content-grid">
              <div className="chart-card">
                <h3 className="card-title">Exam Performance Distribution</h3>
                <div className="distribution-placeholder">
                  <PieChart className="placeholder-icon" />
                  <p className="placeholder-text">Performance chart visualization would appear here</p>
                  <p className="placeholder-subtext">Grade distribution across all exams</p>
                </div>
              </div>
              
              <div className="chart-card">
                <h3 className="card-title">Trend Analysis</h3>
                <div className="distribution-placeholder">
                  <BarChart2 className="placeholder-icon" />
                  <p className="placeholder-text">Trend chart visualization would appear here</p>
                  <p className="placeholder-subtext">Performance trends over the last 6 months</p>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
      
      <style>{`
        .dashboard-container {
          display: flex;
          min-height: 100vh;
          background-color: #f9fafb;
        }

        .dashboard-content {
          flex: 1;
          padding: 2rem;
        }

        .dashboard-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .dashboard-title {
          font-size: 1.875rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .dashboard-actions {
          display: flex;
          gap: 1rem;
        }

        .action-button {
          display: flex;
          align-items: center;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .action-button.primary {
          background-color: #4f46e5;
          color: white;
          border: none;
        }

        .action-button.primary:hover {
          background-color: #4338ca;
        }

        .action-button.secondary {
          background-color: white;
          color: #4b5563;
          border: 1px solid #e5e7eb;
        }

        .action-button.secondary:hover {
          background-color: #f9fafb;
        }

        .action-icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.5rem;
        }

        .nav-container {
          background-color: white;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 2rem;
        }

        .dashboard-nav {
          display: flex;
          border-bottom: 1px solid #e5e7eb;
        }

        .nav-item {
          padding: 1rem 1.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
          background: none;
          border: none;
          cursor: pointer;
          transition: all 0.2s;
          position: relative;
        }

        .nav-item:hover {
          color: #4f46e5;
        }

        .nav-item.active {
          color: #4f46e5;
          font-weight: 600;
        }

        .nav-item.active::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #4f46e5;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
          .stats-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .stat-card {
          background-color: white;
          border-radius: 0.5rem;
          padding: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .stat-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 0.75rem;
        }

        .stat-title {
          font-size: 1rem;
          font-weight: 600;
          color: #4b5563;
          margin: 0;
        }

        .stat-icon {
          color: #4f46e5;
        }

        .stat-value {
          font-size: 1.875rem;
          font-weight: 700;
          color: #4f46e5;
          margin: 0;
        }

        .stat-meta {
          font-size: 0.875rem;
          color: #6b7280;
          margin-top: 0.5rem;
        }

        .stat-meta.positive {
          color: #10b981;
        }

        .stat-meta.negative {
          color: #ef4444;
        }

        .main-content-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1.5rem;
          margin-bottom: 2rem;
        }

        @media (min-width: 1024px) {
          .main-content-grid {
            grid-template-columns: 1fr 1fr;
          }
        }

        .chart-card, .activity-card {
          background-color: white;
          border-radius: 0.5rem;
          padding: 1.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          margin-bottom: 1.5rem;
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1rem;
        }

        .header-actions {
          display: flex;
          gap: 0.5rem;
        }

        .card-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #374151;
          margin: 0;
        }

        .card-action {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4f46e5;
          background: none;
          border: none;
          cursor: pointer;
          padding: 0;
        }

        .card-action:hover {
          color: #4338ca;
          text-decoration: underline;
        }

        .card-action.primary {
          background-color: #4f46e5;
          color: white;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          text-decoration: none;
        }

        .card-action.primary:hover {
          background-color: #4338ca;
          text-decoration: none;
        }

        .card-action.secondary {
          background-color: white;
          color: #4b5563;
          border: 1px solid #e5e7eb;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          text-decoration: none;
        }

        .card-action.secondary:hover {
          background-color: #f9fafb;
          text-decoration: none;
        }

        .exams-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .exam-item {
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
        }

        .exam-item:last-child {
          border-bottom: none;
          padding-bottom: 0;
        }

        .exam-info {
          display: flex;
          flex-direction: column;
        }

        .exam-subject {
          font-weight: 500;
          color: #111827;
          margin: 0 0 0.25rem 0;
        }

        .exam-date {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }

        .exam-status {
          display: inline-flex;
          align-items: center;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 500;
        }

        .exam-status.completed {
          background-color: #d1fae5;
          color: #065f46;
        }

        .exam-status.scheduled {
          background-color: #e0e7ff;
          color: #4338ca;
        }

        .exam-status.draft {
          background-color: #f3f4f6;
          color: #6b7280;
        }

        .activity-list {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .activity-item {
          display: flex;
          gap: 1rem;
        }

        .activity-icon {
          flex-shrink: 0;
          width: 2.5rem;
          height: 2.5rem;
          border-radius: 9999px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .activity-icon.alert {
          background-color: #fee2e2;
          color: #b91c1c;
        }

        .icon-small {
          width: 1.25rem;
          height: 1.25rem;
        }

        .icon-medium {
          width: 1.5rem;
          height: 1.5rem;
        }

        .activity-content {
          flex: 1;
        }

        .activity-title {
          font-size: 0.875rem;
          font-weight: 500;
          color: #111827;
          margin: 0;
        }

        .activity-description {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0.25rem 0 0 0;
        }

        .activity-time {
          font-size: 0.75rem;
          color: #9ca3af;
          margin: 0.25rem 0 0 0;
        }

        .progress-list {
          display: flex;
          flex-direction: column;
          gap: 1.25rem;
        }

        .progress-item {
          display: flex;
          flex-direction: column;
        }

        .progress-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.5rem;
        }

        .progress-title, .progress-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
        }

        .progress-value {
          font-size: 0.875rem;
          font-weight: 500;
          color: #4f46e5;
        }

        .progress-bar-bg {
          width: 100%;
          height: 0.5rem;
          background-color: #e5e7eb;
          border-radius: 9999px;
          overflow: hidden;
        }

        .progress-bar-fill {
          height: 100%;
          background-color: #4f46e5;
          border-radius: 9999px;
        }

        .progress-meta {
          font-size: 0.75rem;
          color: #6b7280;
          margin-top: 0.5rem;
        }

        .chart-container {
          height: 16rem;
        }

        .empty-state {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 10rem;
          color: #6b7280;
          font-size: 0.875rem;
        }

        .courses-grid {
          display: grid;
          grid-template-columns: repeat(1, 1fr);
          gap: 1.5rem;
        }

        @media (min-width: 640px) {
          .courses-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        @media (min-width: 1024px) {
          .courses-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .course-card {
          background-color: white;
          border-radius: 0.5rem;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          border: 1px solid #e5e7eb;
          display: flex;
          flex-direction: column;
        }

        .course-header {
          height: 8rem;
          background: linear-gradient(to right, #4f46e5, #6366f1);
        }

        .course-content {
          padding: 1.5rem;
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .course-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: #111827;
          margin: 0 0 1rem 0;
        }

        .course-meta {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: #6b7280;
          font-size: 0.875rem;
          margin-bottom: 1.5rem;
        }

        .course-progress {
          margin-bottom: 1.5rem;
        }

        .course-actions {
          display: flex;
          justify-content: space-between;
          margin-top: auto;
        }

        .course-action {
          font-size: 0.875rem;
          border: none;
          background: none;
          padding: 0;
          cursor: pointer;
        }

        .course-action.primary {
          color: #4f46e5;
          font-weight: 500;
        }

        .course-action.primary:hover {
          color: #4338ca;
          text-decoration: underline;
        }

                .course-action.secondary {
                  color: #6b7280;
                  font-weight: 500;
                }
                .course-action.secondary:hover {
                  color: #4b5563;
                  text-decoration: underline;
                }
        .add-new {
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1.5rem;
          text-align: center;
          cursor: pointer;
          transition: background 0.2s;
        }
        .add-new:hover {
          background-color: #e5e7eb;
        }
        .add-new-icon {
          background-color: #e0f2fe;
          border-radius: 9999px;
          padding: 0.5rem;
          margin-bottom: 1rem;
          color: #3b82f6;
        }
        .add-new-title {
          font-size: 1rem;
          font-weight: 600;
          color: #111827;
          margin: 0 0 0.5rem 0;
        }
        .add-new-description {
          font-size: 0.875rem;
          color: #6b7280;
          margin: 0;
        }
        .table-container {
          overflow-x: auto;
          border-radius: 0.5rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          background-color: white;
          border: 1px solid #e5e7eb;
          margin-bottom: 2rem;
        }
        .exams-table {
          width: 100%;
          border-collapse: collapse;
          font-size: 0.875rem;
          color: #374151;
        }
        .exams-table th {
          padding: 1rem 1.5rem;
          text-align: left;
          font-size: 0.875rem;
          font-weight: 600;
          color: #374151;
          background-color: #f3f4f6;
          border-bottom: 1px solid #e5e7eb;
        }
        .exams-table td {
          padding: 1rem 1.5rem;
          border-bottom: 1px solid #e5e7eb;
          color: #6b7280;
        }
        .exams-table tr:hover {
          background-color: #f9fafb;
        }
        .exam-title-cell {
          font-weight: 500;
          color: #111827;
        }
              `}
              </style>
            </div>
          );
        };
        
        export default Dashboard;