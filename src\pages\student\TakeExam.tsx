import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { CameraIcon, Mic, ClockIcon, AlertTriangle, CheckCircleIcon, XCircleIcon } from 'lucide-react';

// Mock AI proctoring functions (in a real app, these would connect to actual AI services)
interface AIProctoringResult {
    warnings: string[];
    isMonitoring: boolean;
    startMonitoring: () => void;
}

interface UseAIProctoringProps {
    videoRef: React.RefObject<HTMLVideoElement> | React.RefObject<HTMLVideoElement | null>;
}

const useAIProctoring = (
    videoRef: React.RefObject<HTMLVideoElement> | React.RefObject<HTMLVideoElement | null>
): AIProctoringResult => {
    const [warnings, setWarnings] = useState<string[]>([]);
    const [isMonitoring, setIsMonitoring] = useState<boolean>(false);

    const startMonitoring = (): void => {
        setIsMonitoring(true);
        // Simulate AI analysis
        const interval = setInterval(() => {
            // Randomly generate warnings (mock AI detection)
            const possibleWarnings: string[] = [
                'Multiple faces detected',
                'Looking away from screen',
                'Possible phone usage',
                'Unusual background noise',
                'Tab switching detected'
            ];
            if (Math.random() > 0.7) {
                setWarnings(prev => [
                    ...prev.slice(-2),
                    possibleWarnings[Math.floor(Math.random() * possibleWarnings.length)]
                ]);
            }
        }, 5000);
        // No return statement here; cleanup should be handled elsewhere
    };

    return { warnings, isMonitoring, startMonitoring };
};

const TakeExam = () => {
  const { examId } = useParams();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [timeLeft, setTimeLeft] = useState(3600); // 60 minutes in seconds
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<(string | File | undefined)[]>([]);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [recordingStatus, setRecordingStatus] = useState('idle');
  const { warnings, isMonitoring, startMonitoring } = useAIProctoring(videoRef);
  
  // Mock exam questions
  const examQuestions = [
    {
      id: 1,
      text: "Explain the concept of closures in JavaScript and provide a practical example.",
      type: "essay",
      points: 20,
      timeLimit: 600 // 10 minutes
    },
    {
      id: 2,
      text: "Compare and contrast REST and GraphQL APIs, listing three advantages of each approach.",
      type: "essay",
      points: 15,
      timeLimit: 450 // 7.5 minutes
    },
    {
      id: 3,
      text: "Upload your solution to the following problem: Create a React component that implements a debounce hook.",
      type: "file_upload",
      points: 25,
      timeLimit: 900 // 15 minutes
    }
  ];

  // Initialize media and monitoring
  useEffect(() => {
    const enableMedia = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        setMediaStream(stream);
        startMonitoring();
      } catch (err) {
        console.error("Error accessing media devices:", err);
      }
    };

    enableMedia();

    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Question timer effect
  useEffect(() => {
    setTimeLeft(examQuestions[currentQuestion].timeLimit);
  }, [currentQuestion]);

interface FormatTime {
    (seconds: number): string;
}

const formatTime: FormatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

interface AnswerChangeEvent extends React.ChangeEvent<HTMLTextAreaElement> {}

const handleAnswerChange = (e: AnswerChangeEvent): void => {
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = e.target.value;
    setAnswers(newAnswers);
};

interface FileUploadEvent extends React.ChangeEvent<HTMLInputElement> {}

const handleFileUpload = (e: FileUploadEvent): void => {
    const file = e.target.files && e.target.files[0];
    if (file) {
        const newAnswers = [...answers];
        newAnswers[currentQuestion] = file;
        setAnswers(newAnswers);
    }
};

  const handleNextQuestion = () => {
    if (currentQuestion < examQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const handleSubmitExam = () => {
    // In a real app, this would send answers to the server
    console.log("Submitting exam with answers:", answers);
    alert("Exam submitted successfully!");
  };

  const startRecording = () => {
    setRecordingStatus('recording');
    // In a real app, this would start actual recording
  };

  const stopRecording = () => {
    setRecordingStatus('stopped');
    // In a real app, this would stop recording and save
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="flex justify-between items-center mb-8 bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold text-gray-800">Exam: {examId}</h1>
        <div className="flex items-center gap-2 text-lg font-semibold text-primary-600">
          <ClockIcon className="w-5 h-5" />
          <span>Total Time: {formatTime(timeLeft)}</span>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-200">
            <span className="text-lg font-semibold text-gray-700">Question {currentQuestion + 1} of {examQuestions.length}</span>
            <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">{examQuestions[currentQuestion].points} points</span>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <ClockIcon className="w-4 h-4" />
              <span>Time left: {formatTime(timeLeft)}</span>
            </div>
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-800 mb-4">{examQuestions[currentQuestion].text}</h3>

            {examQuestions[currentQuestion].type === 'essay' ? (
              <textarea
                className="w-full h-64 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 resize-none"
                value={typeof answers[currentQuestion] === 'string' ? answers[currentQuestion] : ''}
                onChange={handleAnswerChange}
                placeholder="Write your answer here..."
              />
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary-400 transition-colors">
                <input
                  type="file"
                  id="fileUpload"
                  onChange={handleFileUpload}
                  className="hidden"
                />
                <label htmlFor="fileUpload" className="cursor-pointer">
                  <div className="text-gray-600">
                    {answers[currentQuestion] && answers[currentQuestion] instanceof File
                      ? `File uploaded: ${(answers[currentQuestion] as File).name}`
                      : 'Click to upload your solution'}
                  </div>
                </label>
              </div>
            )}
          </div>

          <div className="flex justify-between">
            <button
              className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              onClick={handlePreviousQuestion}
              disabled={currentQuestion === 0}
            >
              Previous Question
            </button>
            {currentQuestion < examQuestions.length - 1 ? (
              <button
                className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                onClick={handleNextQuestion}
              >
                Next Question
              </button>
            ) : (
              <button
                className="px-6 py-2 bg-success-600 text-white rounded-lg hover:bg-success-700 transition-colors"
                onClick={handleSubmitExam}
              >
                Submit Exam
              </button>
            )}
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="relative">
              <video ref={videoRef} autoPlay muted className="w-full h-48 bg-gray-900 rounded-lg object-cover" />
              <div className="absolute top-2 right-2 flex items-center gap-2 bg-black bg-opacity-75 text-white px-3 py-1 rounded-full text-sm">
                {recordingStatus === 'recording' ? (
                  <>
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span>Recording</span>
                  </>
                ) : (
                  <span>Camera Active</span>
                )}
              </div>
            </div>

            <div className="mt-4">
              <button
                type="button"
                className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                  recordingStatus === 'recording'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-primary-600 hover:bg-primary-700 text-white'
                }`}
                onClick={recordingStatus === 'recording' ? stopRecording : startRecording}
              >
                {recordingStatus === 'recording' ? 'Stop Recording' : 'Start Recording'}
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-800 mb-4">
              <AlertTriangle className="w-5 h-5 text-warning-500" />
              AI Proctoring Alerts
            </h3>
            {warnings.length > 0 ? (
              <ul className="space-y-2">
                {warnings.map((warning, index) => (
                  <li key={index} className="flex items-center gap-2 text-sm text-warning-700 bg-warning-50 p-2 rounded">
                    <AlertTriangle className="w-4 h-4" />
                    {warning}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="flex items-center gap-2 text-success-700 bg-success-50 p-3 rounded">
                <CheckCircleIcon className="w-5 h-5" />
                <span>No suspicious activity detected</span>
              </div>
            )}
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">System Status</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <CameraIcon className="w-5 h-5 text-gray-600" />
                <span className="text-sm">Camera: {mediaStream ? 'Active' : 'Inactive'}</span>
              </div>
              <div className="flex items-center gap-3">
                <Mic className="w-5 h-5 text-gray-600" />
                <span className="text-sm">Microphone: {mediaStream ? 'Active' : 'Inactive'}</span>
              </div>
              <div className="flex items-center gap-3">
                {isMonitoring ? (
                  <CheckCircleIcon className="w-5 h-5 text-success-600" />
                ) : (
                  <XCircleIcon className="w-5 h-5 text-danger-600" />
                )}
                <span className="text-sm">AI Proctoring: {isMonitoring ? 'Active' : 'Inactive'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TakeExam;
