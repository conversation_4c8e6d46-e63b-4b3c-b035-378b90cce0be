import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { CameraIcon, Mic, ClockIcon, AlertTriangle, CheckCircleIcon, XCircleIcon } from 'lucide-react';

// Mock AI proctoring functions (in a real app, these would connect to actual AI services)
interface AIProctoringResult {
    warnings: string[];
    isMonitoring: boolean;
    startMonitoring: () => void;
}

interface UseAIProctoringProps {
    videoRef: React.RefObject<HTMLVideoElement> | React.RefObject<HTMLVideoElement | null>;
}

const useAIProctoring = (
    videoRef: React.RefObject<HTMLVideoElement> | React.RefObject<HTMLVideoElement | null>
): AIProctoringResult => {
    const [warnings, setWarnings] = useState<string[]>([]);
    const [isMonitoring, setIsMonitoring] = useState<boolean>(false);

    const startMonitoring = (): void => {
        setIsMonitoring(true);
        // Simulate AI analysis
        const interval = setInterval(() => {
            // Randomly generate warnings (mock AI detection)
            const possibleWarnings: string[] = [
                'Multiple faces detected',
                'Looking away from screen',
                'Possible phone usage',
                'Unusual background noise',
                'Tab switching detected'
            ];
            if (Math.random() > 0.7) {
                setWarnings(prev => [
                    ...prev.slice(-2),
                    possibleWarnings[Math.floor(Math.random() * possibleWarnings.length)]
                ]);
            }
        }, 5000);
        // No return statement here; cleanup should be handled elsewhere
    };

    return { warnings, isMonitoring, startMonitoring };
};

const TakeExam = () => {
  const { examId } = useParams();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [timeLeft, setTimeLeft] = useState(3600); // 60 minutes in seconds
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<(string | File | undefined)[]>([]);
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [recordingStatus, setRecordingStatus] = useState('idle');
  const { warnings, isMonitoring, startMonitoring } = useAIProctoring(videoRef);
  
  // Mock exam questions
  const examQuestions = [
    {
      id: 1,
      text: "Explain the concept of closures in JavaScript and provide a practical example.",
      type: "essay",
      points: 20,
      timeLimit: 600 // 10 minutes
    },
    {
      id: 2,
      text: "Compare and contrast REST and GraphQL APIs, listing three advantages of each approach.",
      type: "essay",
      points: 15,
      timeLimit: 450 // 7.5 minutes
    },
    {
      id: 3,
      text: "Upload your solution to the following problem: Create a React component that implements a debounce hook.",
      type: "file_upload",
      points: 25,
      timeLimit: 900 // 15 minutes
    }
  ];

  // Initialize media and monitoring
  useEffect(() => {
    const enableMedia = async () => {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: true,
          audio: true
        });
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        setMediaStream(stream);
        startMonitoring();
      } catch (err) {
        console.error("Error accessing media devices:", err);
      }
    };

    enableMedia();

    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  // Timer effect
  useEffect(() => {
    if (timeLeft <= 0) return;

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Question timer effect
  useEffect(() => {
    setTimeLeft(examQuestions[currentQuestion].timeLimit);
  }, [currentQuestion]);

interface FormatTime {
    (seconds: number): string;
}

const formatTime: FormatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
};

interface AnswerChangeEvent extends React.ChangeEvent<HTMLTextAreaElement> {}

const handleAnswerChange = (e: AnswerChangeEvent): void => {
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = e.target.value;
    setAnswers(newAnswers);
};

interface FileUploadEvent extends React.ChangeEvent<HTMLInputElement> {}

const handleFileUpload = (e: FileUploadEvent): void => {
    const file = e.target.files && e.target.files[0];
    if (file) {
        const newAnswers = [...answers];
        newAnswers[currentQuestion] = file;
        setAnswers(newAnswers);
    }
};

  const handleNextQuestion = () => {
    if (currentQuestion < examQuestions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const handleSubmitExam = () => {
    // In a real app, this would send answers to the server
    console.log("Submitting exam with answers:", answers);
    alert("Exam submitted successfully!");
  };

  const startRecording = () => {
    setRecordingStatus('recording');
    // In a real app, this would start actual recording
  };

  const stopRecording = () => {
    setRecordingStatus('stopped');
    // In a real app, this would stop recording and save
  };

  return (
    <div className="exam-container">
      <div className="exam-header">
        <h1>Exam: {examId}</h1>
        <div className="exam-timer">
          <ClockIcon className="icon" />
          <span>Total Time: {formatTime(timeLeft)}</span>
        </div>
      </div>

      <div className="exam-layout">
        <div className="question-section">
          <div className="question-header">
            <span className="question-number">Question {currentQuestion + 1} of {examQuestions.length}</span>
            <span className="question-points">{examQuestions[currentQuestion].points} points</span>
            <div className="question-timer">
              <ClockIcon className="icon" />
              <span>Time left: {formatTime(timeLeft)}</span>
            </div>
          </div>

          <div className="question-content">
            <h3>{examQuestions[currentQuestion].text}</h3>
            
            {examQuestions[currentQuestion].type === 'essay' ? (
              <textarea
                className="answer-input"
                value={typeof answers[currentQuestion] === 'string' ? answers[currentQuestion] : ''}
                onChange={handleAnswerChange}
                placeholder="Write your answer here..."
              />
            ) : (
              <div className="file-upload-container">
                <input
                  type="file"
                  id="fileUpload"
                  onChange={handleFileUpload}
                  className="file-input"
                />
                <label htmlFor="fileUpload" className="upload-button">
                  {answers[currentQuestion] && answers[currentQuestion] instanceof File
                    ? `File uploaded: ${(answers[currentQuestion] as File).name}`
                    : 'Click to upload your solution'}
                </label>
              </div>
            )}
          </div>

          <div className="question-navigation">
            <button
              className="nav-button prev-button"
              onClick={handlePreviousQuestion}
              disabled={currentQuestion === 0}
            >
              Previous Question
            </button>
            {currentQuestion < examQuestions.length - 1 ? (
              <button
                className="nav-button next-button"
                onClick={handleNextQuestion}
              >
                Next Question
              </button>
            ) : (
              <button
                className="submit-button"
                onClick={handleSubmitExam}
              >
                Submit Exam
              </button>
            )}
          </div>
        </div>

        <div className="proctoring-section">
          <div className="video-feed">
            <video ref={videoRef} autoPlay muted className="camera-feed" />
            <div className="recording-indicator">
              {recordingStatus === 'recording' ? (
                <>
                  <div className="recording-dot"></div>
                  <span>Recording</span>
                </>
              ) : (
                <span>Camera Active</span>
              )}
            </div>
          </div>

          <div className="proctoring-controls">
            <button
              className={`control-button ${recordingStatus === 'recording' ? 'stop-button' : 'record-button'}`}
              onClick={recordingStatus === 'recording' ? stopRecording : startRecording}
            >
              {recordingStatus === 'recording' ? 'Stop Recording' : 'Start Recording'}
            </button>
          </div>

          <div className="ai-warnings">
            <h3>
              <AlertTriangle className="icon" />
              AI Proctoring Alerts
            </h3>
            {warnings.length > 0 ? (
              <ul>
                {warnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            ) : (
              <div className="no-warnings">
                <CheckCircleIcon className="icon" />
                <span>No suspicious activity detected</span>
              </div>
            )}
          </div>

          <div className="system-check">
            <h3>System Status</h3>
            <div className="status-item">
              <CameraIcon className="icon" />
              <span>Camera: {mediaStream ? 'Active' : 'Inactive'}</span>
            </div>
            <div className="status-item">
              <Mic className="icon" />
              <span>Microphone: {mediaStream ? 'Active' : 'Inactive'}</span>
            </div>
            <div className="status-item">
              {isMonitoring ? (
                <CheckCircleIcon className="icon success" />
              ) : (
                <XCircleIcon className="icon error" />
              )}
              <span>AI Proctoring: {isMonitoring ? 'Active' : 'Inactive'}</span>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .exam-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          padding: 2rem;
        }

        .exam-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .exam-header h1 {
          font-size: 1.75rem;
          font-weight: 700;
          color: #1f2937;
          margin: 0;
        }

        .exam-timer {
          display: flex;
          align-items: center;
          background: #fef3c7;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          color: #92400e;
        }

        .exam-timer .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .exam-layout {
          display: grid;
          grid-template-columns: 2fr 1fr;
          gap: 2rem;
        }

        .question-section {
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 2rem;
        }

        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #f3f4f6;
        }

        .question-number {
          font-weight: 600;
          color: #4f46e5;
        }

        .question-points {
          background: #e0f2fe;
          color: #0369a1;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .question-timer {
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #ef4444;
          font-weight: 500;
        }

        .question-timer .icon {
          width: 1rem;
          height: 1rem;
          margin-right: 0.25rem;
        }

        .question-content {
          margin-bottom: 2rem;
        }

        .question-content h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1.5rem 0;
        }

        .answer-input {
          width: 100%;
          min-height: 200px;
          padding: 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-family: inherit;
          font-size: 0.875rem;
          resize: vertical;
        }

        .answer-input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .file-upload-container {
          margin-top: 1rem;
        }

        .file-input {
          display: none;
        }

        .upload-button {
          display: block;
          padding: 1.5rem;
          border: 2px dashed #d1d5db;
          border-radius: 0.375rem;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s;
        }

        .upload-button:hover {
          border-color: #818cf8;
          background: #f8fafc;
        }

        .question-navigation {
          display: flex;
          justify-content: space-between;
          padding-top: 1.5rem;
          border-top: 1px solid #f3f4f6;
        }

        .nav-button {
          padding: 0.75rem 1.5rem;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .prev-button {
          background: #f3f4f6;
          color: #4b5563;
          border: none;
        }

        .prev-button:hover:not(:disabled) {
          background: #e5e7eb;
        }

        .prev-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .next-button {
          background: #4f46e5;
          color: white;
          border: none;
        }

        .next-button:hover {
          background: #4338ca;
        }

        .submit-button {
          padding: 0.75rem 1.5rem;
          background: #10b981;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .submit-button:hover {
          background: #059669;
        }

        .proctoring-section {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .video-feed {
          background: black;
          border-radius: 0.375rem;
          overflow: hidden;
          position: relative;
        }

        .camera-feed {
          width: 100%;
          display: block;
        }

        .recording-indicator {
          position: absolute;
          top: 0.5rem;
          left: 0.5rem;
          display: flex;
          align-items: center;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 0.25rem 0.5rem;
          border-radius: 0.25rem;
          font-size: 0.75rem;
        }

        .recording-dot {
          width: 0.5rem;
          height: 0.5rem;
          background: #ef4444;
          border-radius: 50%;
          margin-right: 0.25rem;
          animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        .proctoring-controls {
          background: white;
          border-radius: 0.375rem;
          padding: 1rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .control-button {
          width: 100%;
          padding: 0.75rem;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          border: none;
        }

        .record-button {
          background: #ef4444;
          color: white;
        }

        .record-button:hover {
          background: #dc2626;
        }

        .stop-button {
          background: #f3f4f6;
          color: #4b5563;
        }

        .stop-button:hover {
          background: #e5e7eb;
        }

        .ai-warnings {
          background: white;
          border-radius: 0.375rem;
          padding: 1rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .ai-warnings h3 {
          display: flex;
          align-items: center;
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .ai-warnings .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
          color: #ef4444;
        }

        .ai-warnings ul {
          margin: 0;
          padding-left: 1.5rem;
        }

        .ai-warnings li {
          font-size: 0.875rem;
          color: #b91c1c;
          margin-bottom: 0.5rem;
          position: relative;
        }

        .ai-warnings li:before {
          content: '•';
          position: absolute;
          left: -1rem;
          color: #ef4444;
          font-weight: bold;
        }

        .no-warnings {
          display: flex;
          align-items: center;
          font-size: 0.875rem;
          color: #166534;
        }

        .no-warnings .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .system-check {
          background: white;
          border-radius: 0.375rem;
          padding: 1rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .system-check h3 {
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 1rem 0;
        }

        .status-item {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
          font-size: 0.875rem;
        }

        .status-item .icon {
          width: 1.25rem;
          height: 1.25rem;
          margin-right: 0.5rem;
        }

        .status-item .success {
          color: #10b981;
        }

        .status-item .error {
          color: #ef4444;
        }

        @media (max-width: 1024px) {
          .exam-layout {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .exam-container {
            padding: 1rem;
          }

          .question-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
          }

          .question-navigation {
            flex-direction: column;
            gap: 0.5rem;
          }

          .nav-button, .submit-button {
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
};

export default TakeExam;