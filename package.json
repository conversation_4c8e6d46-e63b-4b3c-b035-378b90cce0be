{"name": "online-exam-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@heroicons/react": "^2.2.0", "antd": "^5.25.2", "bcrypt": "^6.0.0", "chart.js": "^4.4.9", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "nodemailer": "^7.0.3", "pg": "^8.16.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "styled-jsx": "^5.1.7"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.7", "@types/antd": "^0.12.32", "@types/chart.js": "^2.9.41", "@types/react": "^19.1.2", "@types/react-chartjs-2": "^2.0.2", "@types/react-dom": "^19.1.2", "@types/recharts": "^1.8.29", "@vitejs/plugin-react-swc": "^3.9.0", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.1.10", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}