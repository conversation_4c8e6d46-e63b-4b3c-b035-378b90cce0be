import { NavLink } from 'react-router-dom';
import { 
  Gauge, 
  Users, 
  Shield, 
  Settings, 
  FileText, 
  LogOut 
} from 'lucide-react';

const AdminSidebar = () => {
  const links = [
    { to: '/admin/dashboard', label: 'Dashboard', icon: <Gauge size={18} /> },
    { to: '/admin/users', label: 'Users', icon: <Users size={18} /> },
    { to: '/admin/roles', label: 'Roles & Permissions', icon: <Shield size={18} /> },
    { to: '/admin/settings', label: 'Settings', icon: <Settings size={18} /> },
    { to: '/admin/logs', label: 'Audit Logs', icon: <FileText size={18} /> },
    { to: '/login', label: 'Logout', icon: <LogOut size={18} /> },
  ];

  return (
    <div className="w-64 bg-slate-800 text-white min-h-screen flex flex-col">
      <div className="p-6 border-b border-slate-700">
        <h1 className="text-xl font-bold">Admin Panel</h1>
      </div>
      <nav className="flex-1 py-2 flex flex-col">
        {links.map((item, idx) => (
          <NavLink
            key={idx}
            to={item.to}
            className={({ isActive }) =>
              `flex items-center px-6 py-3 mx-2 rounded-md text-slate-300 no-underline transition-all duration-200 ${
                isActive
                  ? 'bg-primary-600 text-white'
                  : 'hover:bg-slate-700 hover:text-white'
              }`
            }
          >
            <span className="mr-3">{item.icon}</span>
            <span>{item.label}</span>
          </NavLink>
        ))}
      </nav>
    </div>
  );
};

export default AdminSidebar;
