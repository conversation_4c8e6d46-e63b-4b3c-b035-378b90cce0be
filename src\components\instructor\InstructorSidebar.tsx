import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  LayoutDashboard,
  FilePlus,
  User,
  Edit,
  Library,
  BarChart2,
  HelpCircle,
  LogOut
} from 'lucide-react';

type MenuItem = {
  key: string;
  label: string;
  to: string;
  icon: React.ReactNode;
};

const InstructorSidebar = () => {
  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: 'Dashboard',
      to: '/instructor/dashboard',
      icon: <LayoutDashboard size={18} />
    },
    {
      key: 'create',
      label: 'Create Exam',
      to: '/instructor/exam/new',
      icon: <FilePlus size={18} />
    },
    {
      key: 'edit',
      label: 'Edit Exam',
      to: '/instructor/exam/123/edit',
      icon: <Edit size={18} />
    },
    {
      key: 'questions',
      label: 'Question Bank',
      to: '/instructor/questions',
      icon: <Library size={18} />
    },
    {
      key: 'analytics',
      label: 'Analytics',
      to: '/instructor/exam/123/analytics',
      icon: <BarChart2 size={18} />
    },
    {
      key: 'profile',
      label: 'Profile',
      to: '/instructor/profile',
      icon: <User size={18} />
    },
    {
      key: 'help',
      label: 'help',
      to: '/instructor/help',
      icon: <HelpCircle size={18} />
    },
    {
      key: 'logout',
      label: 'Logout',
      to: '/login',
      icon: <LogOut size={18} />
    },
  ];

  return (
    <div className="w-64 bg-slate-800 text-white min-h-screen flex flex-col">
      <div className="p-6 border-b border-slate-700">
        <h1 className="text-xl font-bold">Instructor Panel</h1>
      </div>
      <nav className="flex-1 py-2 flex flex-col">
        {menuItems.map(item => (
          <NavLink
            key={item.key}
            to={item.to}
            className={({ isActive }) => {
              const isLogout = item.to === '/login';
              const baseClasses = "flex items-center px-6 py-3 mx-2 rounded-md no-underline transition-all duration-200 text-sm font-medium";

              if (isLogout) {
                return `${baseClasses} mt-auto mb-4 text-red-400 hover:bg-red-900 hover:text-red-200 ${
                  isActive ? 'bg-red-900 text-red-200' : ''
                }`;
              }

              return `${baseClasses} text-slate-300 ${
                isActive
                  ? 'bg-primary-600 text-white'
                  : 'hover:bg-slate-700 hover:text-white'
              }`;
            }}
          >
            <span className="mr-3">{item.icon}</span>
            <span>{item.label}</span>
          </NavLink>
        ))}
      </nav>
    </div>
  );
};

export default InstructorSidebar;
