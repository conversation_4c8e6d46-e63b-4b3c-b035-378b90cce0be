import React, { useState } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Trash2, 
  List, 
  CheckSquare, 
  ChevronDown, 
  ChevronUp,
  FileText,
  Check,
  X,
  Edit
} from 'lucide-react';

const QuestionBank = () => {
  const [questions, setQuestions] = useState([
    { 
      id: 1, 
      question: 'What is React?', 
      type: 'MCQ', 
      options: ['A library', 'A framework', 'A language', 'A database'],
      correctAnswer: 0,
      difficulty: 'Easy',
      tags: ['React', 'Frontend'],
      lastUsed: '2023-10-15',
      created: '2023-05-10'
    },
    { 
      id: 2, 
      question: 'Explain useEffect hook in React.', 
      type: 'Short Answer',
      difficulty: 'Medium',
      tags: ['React', 'Hooks'],
      lastUsed: '2023-11-20',
      created: '2023-06-05'
    },
    { 
      id: 3, 
      question: 'What is closure in JavaScript?', 
      type: 'MCQ',
      options: ['A function', 'Scope combination', 'A variable', 'A loop'],
      correctAnswer: 1,
      difficulty: 'Hard',
      tags: ['JavaScript', 'Fundamentals'],
      lastUsed: '2023-09-10',
      created: '2023-04-15'
    },
    { 
      id: 4, 
      question: 'Difference between let and const in JS', 
      type: 'Essay',
      difficulty: 'Easy',
      tags: ['JavaScript', 'Variables'],
      lastUsed: null,
      created: '2023-07-22'
    },
  ]);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('All');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [expandedQuestion, setExpandedQuestion] = useState<number | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  
  // Add New Question Modal States
  const [showAddModal, setShowAddModal] = useState(false);
  const [newQuestion, setNewQuestion] = useState<{
    question: string;
    type: string;
    options: string[];
    correctAnswer: number;
    difficulty: string;
    tags: string[];
    lastUsed: string | null;
    created: string;
  }>({
    question: '',
    type: 'MCQ',
    options: ['', '', '', ''],
    correctAnswer: 0,
    difficulty: 'Medium',
    tags: [],
    lastUsed: null,
    created: new Date().toISOString().split('T')[0]
  });
  
  // Edit Question Modal States
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingQuestion, setEditingQuestion] = useState<any>(null);
  
  const [tagInput, setTagInput] = useState('');

  const allTags = Array.from(new Set(questions.flatMap(q => q.tags || [])));

  const filteredQuestions = questions.filter(q => {
    const matchesSearch = q.question.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'All' || q.type === selectedType;
    const matchesDifficulty = selectedDifficulty === 'All' || q.difficulty === selectedDifficulty;
    const matchesTags = selectedTags.length === 0 || 
      (q.tags && selectedTags.every(tag => q.tags.includes(tag)));
    
    return matchesSearch && matchesType && matchesDifficulty && matchesTags;
  });

  const toggleSelectQuestion = (id: number) => {
    setSelectedQuestions(prev => 
      prev.includes(id) 
        ? prev.filter(qId => qId !== id) 
        : [...prev, id]
    );
  };

  const toggleSelectAll = () => {
    if (selectedQuestions.length === filteredQuestions.length) {
      setSelectedQuestions([]);
    } else {
      setSelectedQuestions(filteredQuestions.map(q => q.id));
    }
  };

  const deleteSelected = () => {
    setQuestions(prev => prev.filter(q => !selectedQuestions.includes(q.id)));
    setSelectedQuestions([]);
  };

  const addToExam = () => {
    console.log('Adding to exam:', selectedQuestions);
    alert(`Added ${selectedQuestions.length} questions to exam`);
  };

  const toggleExpandQuestion = (id: number) => {
    setExpandedQuestion(prev => prev === id ? null : id);
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag) 
        : [...prev, tag]
    );
  };

  // Add New Question Functions
  const openAddModal = () => {
    setShowAddModal(true);
  };

  const closeAddModal = () => {
    setShowAddModal(false);
    setNewQuestion({
      question: '',
      type: 'MCQ',
      options: ['', '', '', ''],
      correctAnswer: 0,
      difficulty: 'Medium',
      tags: [],
      lastUsed: null,
      created: new Date().toISOString().split('T')[0]
    });
    setTagInput('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewQuestion(prev => ({ ...prev, [name]: value }));
  };

  const handleOptionChange = (index: number, value: string) => {
    const updatedOptions = [...newQuestion.options];
    updatedOptions[index] = value;
    setNewQuestion(prev => ({ ...prev, options: updatedOptions }));
  };

  const handleCorrectAnswerChange = (index: number) => {
    setNewQuestion(prev => ({ ...prev, correctAnswer: index }));
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !newQuestion.tags.includes(tagInput.trim())) {
      setNewQuestion(prev => ({ 
        ...prev, 
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setNewQuestion(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = () => {
    if (!newQuestion.question.trim()) {
      alert('Question text is required');
      return;
    }

    if (newQuestion.type === 'MCQ') {
      if (newQuestion.options.some(opt => !opt.trim())) {
        alert('All options must have a value');
        return;
      }
    }

    const newId = Math.max(...questions.map(q => q.id), 0) + 1;
    setQuestions(prev => [
      ...prev,
      {
        ...newQuestion,
        id: newId,
        lastUsed: newQuestion.lastUsed ?? '',
      }
    ]);

    closeAddModal();
  };

  // Edit Question Functions
  const openEditModal = (question: any) => {
    setEditingQuestion(JSON.parse(JSON.stringify(question)));
    setShowEditModal(true);
  };

  const closeEditModal = () => {
    setShowEditModal(false);
    setEditingQuestion(null);
  };

  const handleEditSubmit = () => {
    if (!editingQuestion || !editingQuestion.question.trim()) {
      alert('Question text is required');
      return;
    }

    if (editingQuestion.type === 'MCQ') {
      if (editingQuestion.options.some((opt: string) => !opt.trim())) {
        alert('All options must have a value');
        return;
      }
    }

    setQuestions(prev => 
      prev.map(q => q.id === editingQuestion.id ? editingQuestion : q)
    );
    closeEditModal();
  };

  // Add single question to exam
  const addSingleToExam = (questionId: number) => {
    console.log('Adding question to exam:', questionId);
    alert('Added question to exam');
    
    // Update last used date
    setQuestions(prev => prev.map(q => 
      q.id === questionId 
        ? {...q, lastUsed: new Date().toISOString().split('T')[0]} 
        : q
    ));
  };

  return (
    <div className="question-bank-page">
      <div className="question-bank-header">
        <h1 className="question-bank-title">
          <FileText size={28} className="header-icon" />
          Question Bank
        </h1>
        <button className="add-question-button" onClick={openAddModal}>
          <Plus size={18} className="button-icon" />
          Add New Question
        </button>
      </div>

      <div className="search-filter-container">
        <div className="search-filter-group">
          <div className="search-box">
            <Search className="search-icon" size={18} />
            <input
              type="text"
              placeholder="Search questions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <button 
            onClick={() => setShowFilters(!showFilters)}
            className="filter-toggle-button"
          >
            <Filter size={18} className="button-icon" />
            Filters
            {showFilters ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
          </button>
        </div>

        {showFilters && (
          <div className="advanced-filters">
            <div className="filter-group">
              <label htmlFor="question-type-select">Question Type</label>
              <select
                id="question-type-select"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="All">All Types</option>
                <option value="MCQ">Multiple Choice</option>
                <option value="Short Answer">Short Answer</option>
                <option value="Essay">Essay</option>
              </select>
            </div>
            <div className="filter-group">
              <label>Difficulty</label>
              <select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                aria-label="Select difficulty"
              >
                <option value="All">All Levels</option>
                <option value="Easy">Easy</option>
                <option value="Medium">Medium</option>
                <option value="Hard">Hard</option>
              </select>
            </div>
            <div className="filter-group">
              <label>Tags</label>
              <div className="tags-container">
                {allTags.map(tag => (
                  <button
                    key={tag}
                    onClick={() => toggleTag(tag)}
                    className={`tag ${selectedTags.includes(tag) ? 'selected' : ''}`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {selectedQuestions.length > 0 && (
        <div className="bulk-actions">
          <div className="selected-count">
            <CheckSquare size={18} className="action-icon" />
            <span>{selectedQuestions.length} selected</span>
          </div>
          <div className="action-buttons">
            <button onClick={addToExam} className="action-button add-to-exam">
              <List size={16} className="button-icon" />
              Add to Exam
            </button>
            <button onClick={deleteSelected} className="action-button delete">
              <Trash2 size={16} className="button-icon" />
              Delete
            </button>
          </div>
        </div>
      )}

      <div className="questions-container">
        <div className="questions-header">
          <div className="header-checkbox">
            <input 
              type="checkbox" 
              checked={selectedQuestions.length === filteredQuestions.length && filteredQuestions.length > 0}
              onChange={toggleSelectAll}
              title="Select all questions"
            />
          </div>
          <div className="header-question">Question</div>
          <div className="header-type">Type</div>
          <div className="header-difficulty">Difficulty</div>
        </div>

        {filteredQuestions.length === 0 ? (
          <div className="empty-state">
            No questions found matching your criteria
          </div>
        ) : (
          <ul className="questions-list">
            {filteredQuestions.map(q => (
              <li key={q.id} className="question-item">
                <div className="question-row">
                  <div className="question-checkbox">
                    <input 
                      type="checkbox" 
                      checked={selectedQuestions.includes(q.id)}
                      onChange={() => toggleSelectQuestion(q.id)}
                      title="Select question"
                      aria-label="Select question"
                    />
                  </div>
                  <div 
                    className="question-text"
                    onClick={() => toggleExpandQuestion(q.id)}
                  >
                    {q.question}
                    <button 
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleExpandQuestion(q.id);
                      }}
                      className="expand-button"
                    >
                      {expandedQuestion === q.id ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                    </button>
                  </div>
                  <div className="question-type">
                    <span className={`type-badge ${q.type.toLowerCase().replace(' ', '-')}`}>
                      {q.type}
                    </span>
                  </div>
                  <div className="question-difficulty">
                    <span className={`difficulty-badge ${q.difficulty.toLowerCase()}`}>
                      {q.difficulty}
                    </span>
                  </div>
                </div>

                {expandedQuestion === q.id && (
                  <div className="question-details">
                    <div className="details-grid">
                      <div className="detail-group">
                        <h3>Tags</h3>
                        <div className="tags-list">
                          {q.tags?.map(tag => (
                            <span key={tag} className="tag-badge">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      <div className="detail-group">
                        <h3>Created</h3>
                        <p>{q.created}</p>
                      </div>
                      <div className="detail-group">
                        <h3>Last Used</h3>
                        <p>{q.lastUsed || 'Never'}</p>
                      </div>
                    </div>

                    {q.type === 'MCQ' && (
                      <div className="options-section">
                        <h3>Options</h3>
                        <ul className="options-list">
                          {q.options?.map((option, index) => (
                            <li key={index} className="option-item">
                              {index === q.correctAnswer ? (
                                <Check className="correct-icon" size={16} />
                              ) : (
                                <X className="incorrect-icon" size={16} />
                              )}
                              <span className={index === q.correctAnswer ? 'correct-answer' : ''}>
                                {option}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="detail-actions">
                      <button 
                        className="edit-button"
                        onClick={() => openEditModal(q)}
                      >
                        <Edit size={16} className="button-icon" />
                        Edit
                      </button>
                      <button 
                        className="add-exam-button"
                        onClick={() => addSingleToExam(q.id)}
                      >
                        <List size={16} className="button-icon" />
                        Add to Exam
                      </button>
                    </div>
                  </div>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="questions-footer">
        <div className="footer-stats">
          Showing {filteredQuestions.length} of {questions.length} questions
        </div>
        <div className="footer-pagination">
          <button className="pagination-button">Previous</button>
          <button className="pagination-button">Next</button>
        </div>
      </div>

      {/* Add New Question Modal */}
      {showAddModal && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>Add New Question</h2>
              <button className="close-button" onClick={closeAddModal}>×</button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="question">Question Text</label>
                <textarea
                  id="question"
                  name="question"
                  value={newQuestion.question}
                  onChange={handleInputChange}
                  placeholder="Enter question text"
                  rows={3}
                ></textarea>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="type">Question Type</label>
                  <select
                    id="type"
                    name="type"
                    value={newQuestion.type}
                    onChange={handleInputChange}
                  >
                    <option value="MCQ">Multiple Choice</option>
                    <option value="Short Answer">Short Answer</option>
                    <option value="Essay">Essay</option>
                  </select>
                </div>
                
                <div className="form-group">
                  <label htmlFor="difficulty">Difficulty</label>
                  <select
                    id="difficulty"
                    name="difficulty"
                    value={newQuestion.difficulty}
                    onChange={handleInputChange}
                  >
                    <option value="Easy">Easy</option>
                    <option value="Medium">Medium</option>
                    <option value="Hard">Hard</option>
                  </select>
                </div>
              </div>
              
              {newQuestion.type === 'MCQ' && (
                <div className="options-form">
                  <label>Options</label>
                  {newQuestion.options.map((option, index) => (
                    <div key={index} className="option-form-row">
                      <input 
                        type="radio"
                        checked={newQuestion.correctAnswer === index}
                        onChange={() => handleCorrectAnswerChange(index)}
                        title={`Select as correct answer for Option ${index + 1}`}
                      />
                      <input
                        type="text"
                        value={option}
                        onChange={(e) => handleOptionChange(index, e.target.value)}
                        placeholder={`Option ${index + 1}`}
                        title={`Option ${index + 1}`}
                      />
                    </div>
                  ))}
                </div>
              )}
              
              <div className="form-group">
                <label>Tags</label>
                <div className="tag-input-container">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Enter a tag"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                  <button type="button" onClick={handleAddTag}>Add</button>
                </div>
                
                <div className="selected-tags">
                  {newQuestion.tags.map(tag => (
                    <span key={tag} className="tag-badge">
                      {tag}
                      <button 
                        className="remove-tag" 
                        onClick={() => handleRemoveTag(tag)}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="cancel-button" onClick={closeAddModal}>Cancel</button>
              <button className="submit-button" onClick={handleSubmit}>Add Question</button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Question Modal */}
      {showEditModal && editingQuestion && (
        <div className="modal-overlay">
          <div className="modal-container">
            <div className="modal-header">
              <h2>Edit Question</h2>
              <button className="close-button" onClick={closeEditModal}>×</button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label htmlFor="edit-question">Question Text</label>
                <textarea
                  id="edit-question"
                  name="question"
                  value={editingQuestion.question}
                  onChange={(e) => setEditingQuestion({...editingQuestion, question: e.target.value})}
                  placeholder="Enter question text"
                  rows={3}
                ></textarea>
              </div>
              
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="edit-type">Question Type</label>
                  <select
                    id="edit-type"
                    name="type"
                    value={editingQuestion.type}
                    onChange={(e) => setEditingQuestion({...editingQuestion, type: e.target.value})}
                  >
                    <option value="MCQ">Multiple Choice</option>
                    <option value="Short Answer">Short Answer</option>
                    <option value="Essay">Essay</option>
                  </select>
                </div>
                
                <div className="form-group">
                  <label htmlFor="edit-difficulty">Difficulty</label>
                  <select
                    id="edit-difficulty"
                    name="difficulty"
                    value={editingQuestion.difficulty}
                    onChange={(e) => setEditingQuestion({...editingQuestion, difficulty: e.target.value})}
                  >
                    <option value="Easy">Easy</option>
                    <option value="Medium">Medium</option>
                    <option value="Hard">Hard</option>
                  </select>
                </div>
              </div>
              
              {editingQuestion.type === 'MCQ' && (
                <div className="options-form">
                  <label>Options</label>
                  {editingQuestion.options.map((option: string, index: number) => (
                    <div key={index} className="option-form-row">
                      <input 
                        type="radio"
                        checked={editingQuestion.correctAnswer === index}
                        onChange={() => setEditingQuestion({...editingQuestion, correctAnswer: index})}
                        title={`Select as correct answer for Option ${index + 1}`}
                      />
                      <input
                        type="text"
                        value={option}
                        onChange={(e) => {
                          const newOptions = [...editingQuestion.options];
                          newOptions[index] = e.target.value;
                          setEditingQuestion({...editingQuestion, options: newOptions});
                        }}
                        placeholder={`Option ${index + 1}`}
                        title={`Option ${index + 1}`}
                      />
                    </div>
                  ))}
                </div>
              )}
              
              <div className="form-group">
                <label>Tags</label>
                <div className="tag-input-container">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Enter a tag"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        if (tagInput.trim() && !editingQuestion.tags.includes(tagInput.trim())) {
                          setEditingQuestion({
                            ...editingQuestion,
                            tags: [...editingQuestion.tags, tagInput.trim()]
                          });
                          setTagInput('');
                        }
                      }
                    }}
                  />
                  <button 
                    type="button" 
                    onClick={() => {
                      if (tagInput.trim() && !editingQuestion.tags.includes(tagInput.trim())) {
                        setEditingQuestion({
                          ...editingQuestion,
                          tags: [...editingQuestion.tags, tagInput.trim()]
                        });
                        setTagInput('');
                      }
                    }}
                  >
                    Add
                  </button>
                </div>
                
                <div className="selected-tags">
                  {editingQuestion.tags.map((tag: string) => (
                    <span key={tag} className="tag-badge">
                      {tag}
                      <button 
                        className="remove-tag" 
                        onClick={() => {
                          setEditingQuestion({
                            ...editingQuestion,
                            tags: editingQuestion.tags.filter((t: string) => t !== tag)
                          });
                        }}
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="modal-footer">
              <button className="cancel-button" onClick={closeEditModal}>Cancel</button>
              <button className="submit-button" onClick={handleEditSubmit}>Save Changes</button>
            </div>
          </div>
        </div>
      )}

      <style>{`
        .question-bank-page {
          padding: 2rem;
          max-width: 1200px;
          margin: 0 auto;
        }

        .question-bank-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 2rem;
        }

        .question-bank-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.875rem;
          font-weight: 700;
          color: #15803d;
          margin-bottom: 0.5rem;
        }

        .header-icon {
          color: #15803d;
        }

        .add-question-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .add-question-button:hover {
          background-color: #166534;
        }

        .button-icon {
          margin-right: 0.25rem;
        }

        .search-filter-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1rem;
          margin-bottom: 1.5rem;
        }

        .search-filter-group {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          margin-bottom: 1rem;
        }

        @media (min-width: 768px) {
          .search-filter-group {
            flex-direction: row;
          }
        }

        .search-box {
          position: relative;
          flex-grow: 1;
        }

        .search-icon {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          color: #64748b;
        }

        .search-box input {
          width: 100%;
          padding: 0.5rem 0.75rem 0.5rem 2.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .search-box input:focus {
          outline: none;
          border-color: #86efac;
          box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
        }

        .filter-toggle-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background: none;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-weight: 500;
          color: #64748b;
          cursor: pointer;
          transition: all 0.2s;
        }

        .filter-toggle-button:hover {
          background-color: #f8fafc;
        }

        .advanced-filters {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1rem;
          padding-top: 1rem;
          border-top: 1px solid #e2e8f0;
        }

        @media (min-width: 768px) {
          .advanced-filters {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .filter-group {
          margin-bottom: 1rem;
        }

        .filter-group label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #1e293b;
          margin-bottom: 0.5rem;
        }

        .filter-group select {
          width: 100%;
          padding: 0.5rem 0.75rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
        }

        .tags-container {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .tag {
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          border-radius: 9999px;
          background-color: #f1f5f9;
          border: 1px solid #e2e8f0;
          color: #334155;
          cursor: pointer;
        }

        .tag.selected {
          background-color: #dcfce7;
          border-color: #86efac;
          color: #166534;
        }

        .bulk-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #f0f9ff;
          padding: 0.75rem 1rem;
          border-radius: 0.5rem;
          margin-bottom: 1.5rem;
        }

        .selected-count {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 500;
          color: #0369a1;
        }

        .action-icon {
          color: #0369a1;
        }

        .action-buttons {
          display: flex;
          gap: 0.5rem;
        }

        .action-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .add-to-exam {
          background-color: #15803d;
          color: white;
          border: none;
        }

        .add-to-exam:hover {
          background-color: #166534;
        }

        .delete {
          background-color: #fee2e2;
          color: #b91c1c;
          border: none;
        }

        .delete:hover {
          background-color: #fecaca;
        }

        .questions-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .questions-header {
          display: grid;
          grid-template-columns: 50px 1fr 120px 120px;
          background-color: #f8fafc;
          padding: 0.75rem 1rem;
          border-bottom: 1px solid #e2e8f0;
          font-weight: 500;
          color: #64748b;
        }

        .header-checkbox,
        .question-checkbox {
          display: flex;
          align-items: center;
        }

        .header-question {
          padding-left: 0.5rem;
        }

        .empty-state {
          padding: 2rem;
          text-align: center;
          color: #64748b;
        }

        .questions-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .question-item {
          border-bottom: 1px solid #e2e8f0;
        }

        .question-item:last-child {
          border-bottom: none;
        }

        .question-row {
          display: grid;
          grid-template-columns: 50px 1fr 120px 120px;
          padding: 1rem;
          align-items: center;
        }

        .question-text {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-right: 0.5rem;
          font-weight: 500;
          cursor: pointer;
        }

        .expand-button {
          background: none;
          border: none;
          color: #64748b;
          cursor: pointer;
          padding: 0.25rem;
        }

        .type-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          border-radius: 9999px;
        }

        .type-badge.mcq {
          background-color: #dbeafe;
          color: #1e40af;
        }

        .type-badge.short-answer {
          background-color: #ede9fe;
          color: #5b21b6;
        }

        .type-badge.essay {
          background-color: #fef3c7;
          color: #92400e;
        }

        .difficulty-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          border-radius: 9999px;
        }

        .difficulty-badge.easy {
          background-color: #dcfce7;
          color: #166534;
        }

        .difficulty-badge.medium {
          background-color: #fef9c3;
          color: #854d0e;
        }

        .difficulty-badge.hard {
          background-color: #fee2e2;
          color: #991b1b;
        }

        .question-details {
          padding: 1rem;
          background-color: #f8fafc;
          border-top: 1px solid #e2e8f0;
        }

        .details-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 1.5rem;
          margin-bottom: 1.5rem;
        }

        @media (min-width: 768px) {
          .details-grid {
            grid-template-columns: repeat(3, 1fr);
          }
        }

        .detail-group h3 {
          font-size: 0.875rem;
          font-weight: 500;
          color: #64748b;
          margin-bottom: 0.5rem;
        }

        .tags-list {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
        }

        .tag-badge {
          display: inline-block;
          padding: 0.25rem 0.5rem;
          font-size: 0.75rem;
          background-color: #e2e8f0;
          border-radius: 9999px;
          color: #334155;
        }

        .options-section h3 {
          font-size: 0.875rem;
          font-weight: 500;
          color: #64748b;
          margin-bottom: 0.5rem;
        }

        .options-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .option-item {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 0;
        }

        .correct-icon {
          color: #16a34a;
        }

        .incorrect-icon {
          color: #e2e8f0;
        }

        .correct-answer {
          font-weight: 500;
          color: #166534;
        }

        .detail-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.5rem;
          padding-top: 1rem;
          border-top: 1px solid #e2e8f0;
        }

        .edit-button {
          padding: 0.5rem 1rem;
          background-color: #3b82f6;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        .edit-button:hover {
          background-color: #2563eb;
        }
        .add-exam-button {
          padding: 0.5rem 1rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        .add-exam-button:hover {
          background-color: #166534;
        }
        .questions-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem;
          background-color: #f8fafc;
          border-top: 1px solid #e2e8f0;
        }
        .footer-stats {
          font-size: 0.875rem;
          color: #64748b;
        }
        .footer-pagination {
          display: flex;
          gap: 0.5rem;
        }
        .pagination-button {
          padding: 0.5rem 1rem;
          background-color: #e2e8f0;
          color: #334155;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        .pagination-button:hover {
          background-color: #cbd5e1;
        }
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }
                .modal-container {
                  background-color: white;
                  border-radius: 0.75rem;
                  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                  width: 90%;
                  max-width: 600px;
                  padding: 2rem;
                }
                .modal-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 1.5rem;
                }
                .modal-header h2 {
                  font-size: 1.5rem;
                  font-weight: 700;
                  color: #15803d;
                }
                .close-button {
                  background: none;
                  border: none;
                  font-size: 1.5rem;
                  color: #64748b;
                  cursor: pointer;
                }
                .modal-body {
                  display: flex;
                  flex-direction: column;
                  gap: 1rem;
                }
                .form-group {
                  display: flex;
                  flex-direction: column;
                  gap: 0.5rem;
                }
                .form-group label {
                  font-size: 0.875rem;
                  font-weight: 500;
                  color: #1e293b;
                }
                .form-group textarea,
                .form-group input,
                .form-group select {
                  width: 100%;
                  padding: 0.5rem;
                  border: 1px solid #d1d5db;
                  border-radius: 0.375rem;
                  font-size: 0.875rem;
                  transition: all 0.2s;
                }
                .form-group textarea:focus,
                .form-group input:focus,
                .form-group select:focus {
                  outline: none;
                  border-color: #86efac;
                  box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
                }
                .form-row {
                  display: flex;
                  gap: 1rem;
                }
                .form-row .form-group {
                  flex: 1;
                }
                .options-form {
                  display: flex;
                  flex-direction: column;
                  gap: 1rem;
                }
                .option-form-row {
                  display: flex;
                  align-items: center;
                  gap: 1rem;
                }
                .option-form-row input[type="radio"] {
                  margin-right: 0.5rem;
                }
                .tag-input-container {
                  display: flex;
                  gap: 0.5rem;
                }
                .tag-input-container input {
                  flex-grow: 1;
                }
                .tag-input-container button {
                  padding: 0.5rem 1rem;
                  background-color: #15803d;
                  color: white;
                  border: none;
                  border-radius: 0.375rem;
                  font-weight: 500;
                  cursor: pointer;
                  transition: background-color 0.2s;
                }
                .tag-input-container button:hover {
                  background-color: #166534;
                }
                .selected-tags {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 0.5rem;
                }
                .selected-tags .tag-badge {
                  background-color: #e2e8f0;
                  color: #334155;
                  padding: 0.25rem 0.5rem;
                  border-radius: 9999px;
                }
                .selected-tags .remove-tag {
                  background: none;
                  border: none;
                  color: #dc2626;
                  cursor: pointer;
                  margin-left: 0.25rem;
                }
                .modal-footer {
                  display: flex;
                  justify-content: flex-end;
                  gap: 1rem;
                  margin-top: 1.5rem;
                }
                .cancel-button {
                  padding: 0.5rem 1rem;
                  background-color: #e2e8f0;
                  color: #334155;
                  border: none;
                  border-radius: 0.375rem;
                  font-weight: 500;
                  cursor: pointer;
                  transition: background-color 0.2s;
                }
                .cancel-button:hover {
                  background-color: #cbd5e1;
                }
                .submit-button {
                  padding: 0.5rem 1rem;
                  background-color: #15803d;
                  color: white;
                  border: none;
                  border-radius: 0.375rem;
                  font-weight: 500;
                  cursor: pointer;
                  transition: background-color 0.2s;
                }
                .submit-button:hover {
                  background-color: #166534;
                }
              `}</style>
            </div>
          );
        };
        
        export default QuestionBank;