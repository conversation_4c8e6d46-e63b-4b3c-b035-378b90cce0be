import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // In a real app, you would call your password reset API here
      setSuccess(true);
    } catch (err) {
      setError('An error occurred while processing your request');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="forgot-password-container">
      <div className="forgot-password-card">
        <div className="forgot-password-header">
          <h1>Exam Portal</h1>
          <p>Reset your password</p>
        </div>
        
        <div className="forgot-password-content">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}
          
          {success ? (
            <div className="success-message">
              <h3>Password reset link sent!</h3>
              <p>We've sent instructions to reset your password to your email address.</p>
              <button 
                className="back-to-login"
                onClick={() => navigate('/login')}
              >
                Back to Login
              </button>
            </div>
          ) : (
            <>
              <p className="instructions">
                Enter your email address and we'll send you a link to reset your password.
              </p>
              
              <form onSubmit={handleSubmit} className="forgot-password-form">
                <div className="form-group">
                  <label htmlFor="email">
                    Email Address
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <button
                    type="submit"
                    disabled={isLoading}
                    className={`submit-button ${isLoading ? 'loading' : ''}`}
                  >
                    {isLoading ? (
                      <>
                        <span className="spinner"></span>
                        Sending...
                      </>
                    ) : 'Send Reset Link'}
                  </button>
                </div>
              </form>
            </>
          )}
          
          <div className="login-section">
            <div className="divider">
              <span>Remember your password?</span>
            </div>
            
            <div className="login-button">
              <a href="/login">
                Sign in
              </a>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        .forgot-password-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1rem;
        }

        .forgot-password-card {
          width: 100%;
          max-width: 28rem;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .forgot-password-header {
          background: #4f46e5;
          padding: 1.5rem 2rem;
          text-align: center;
          color: white;
        }

        .forgot-password-header h1 {
          font-size: 1.5rem;
          font-weight: 700;
          margin: 0;
        }

        .forgot-password-header p {
          color: #a5b4fc;
          margin-top: 0.25rem;
          font-size: 0.875rem;
        }

        .forgot-password-content {
          padding: 2rem;
        }

        .error-message {
          margin-bottom: 1rem;
          padding: 0.75rem;
          background: #fef2f2;
          color: #dc2626;
          border-radius: 0.375rem;
          font-size: 0.875rem;
        }

        .success-message {
          margin-bottom: 1rem;
          padding: 1rem;
          background: #f0fdf4;
          color: #16a34a;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          text-align: center;
        }

        .success-message h3 {
          margin: 0 0 0.5rem 0;
          font-size: 1rem;
          font-weight: 600;
        }

        .success-message p {
          margin: 0 0 1rem 0;
          font-size: 0.875rem;
        }

        .back-to-login {
          width: 100%;
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: background 0.2s;
        }

        .back-to-login:hover {
          background: #4338ca;
        }

        .instructions {
          margin: 0 0 1.5rem 0;
          color: #6b7280;
          font-size: 0.875rem;
          text-align: center;
        }

        .forgot-password-form {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-group label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #374151;
        }

        .form-group input {
          width: 100%;
          padding: 0.5rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          transition: all 0.2s;
        }

        .form-group input:focus {
          outline: none;
          border-color: #818cf8;
          box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
        }

        .submit-button {
          width: 100%;
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          transition: background 0.2s;
        }

        .submit-button:hover {
          background: #4338ca;
        }

        .submit-button:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .submit-button.loading {
          opacity: 0.7;
        }

        .spinner {
          width: 1rem;
          height: 1rem;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-radius: 50%;
          border-top-color: white;
          animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
          to { transform: rotate(360deg); }
        }

        .login-section {
          margin-top: 1.5rem;
        }

        .divider {
          position: relative;
          margin: 1.5rem 0;
        }

        .divider::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background: #e5e7eb;
          z-index: 1;
        }

        .divider span {
          position: relative;
          padding: 0 0.5rem;
          background: white;
          color: #6b7280;
          font-size: 0.875rem;
          z-index: 2;
        }

        .login-button a {
          display: block;
          width: 100%;
          padding: 0.5rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          color: #374151;
          text-align: center;
          text-decoration: none;
          transition: all 0.2s;
        }

        .login-button a:hover {
          background: #f9fafb;
          border-color: #9ca3af;
        }
      `}</style>
    </div>
  );
};

export default ForgotPassword;