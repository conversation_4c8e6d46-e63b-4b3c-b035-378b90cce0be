import { useNavigate } from 'react-router-dom';

const TermsOfService = () => {
  const navigate = useNavigate();

  return (
    <div className="tos-container">
      <div className="tos-card">
        <div className="tos-header">
          <h1>Exam Portal</h1>
          <p>Terms of Service</p>
        </div>
        
        <div className="tos-content">
          <div className="tos-section">
            <h2>1. Acceptance of Terms</h2>
            <p>
              By accessing or using the Exam Portal web application ("Service"), you agree to be bound by these Terms of Service ("Terms"). 
              If you do not agree to all the terms and conditions of this agreement, you may not use the Service.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>2. Description of Service</h2>
            <p>
              Exam Portal provides an online platform for educational institutions to conduct examinations and for students to participate 
              in those examinations. The Service includes question presentation, time management, and submission capabilities.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>3. User Responsibilities</h2>
            <p>
              As a user of the Service, you agree to:
            </p>
            <ul>
              <li>Provide accurate and complete registration information</li>
              <li>Maintain the confidentiality of your account credentials</li>
              <li>Not share your account with others</li>
              <li>Not attempt to circumvent any security measures</li>
              <li>Not use the Service for any fraudulent or illegal purpose</li>
            </ul>
          </div>
          
          <div className="tos-section">
            <h2>4. Academic Integrity</h2>
            <p>
              You agree to maintain the highest standards of academic integrity when using the Service. Any form of cheating, including but 
              not limited to:
            </p>
            <ul>
              <li>Unauthorized collaboration</li>
              <li>Use of prohibited materials</li>
              <li>Impersonation</li>
              <li>Sharing of exam content</li>
            </ul>
            <p>
              may result in immediate termination of your account and academic consequences as determined by your institution.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>5. Privacy Policy</h2>
            <p>
              Your use of the Service is also governed by our Privacy Policy, which explains how we collect, use, and protect your 
              personal information. By using the Service, you consent to our collection and use of personal data as outlined in the 
              Privacy Policy.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>6. Intellectual Property</h2>
            <p>
              All content provided through the Service, including but not limited to questions, exams, and interface elements, are the 
              property of Exam Portal or its licensors and are protected by intellectual property laws. You may not reproduce, distribute, 
              or create derivative works without explicit permission.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>7. Service Modifications</h2>
            <p>
              We reserve the right to modify or discontinue, temporarily or permanently, the Service (or any part thereof) with or without 
              notice. We shall not be liable to you or any third party for any modification, suspension, or discontinuance of the Service.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>8. Limitation of Liability</h2>
            <p>
              Exam Portal shall not be liable for any indirect, incidental, special, consequential or punitive damages, including without 
              limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from:
            </p>
            <ul>
              <li>Your access to or use of or inability to access or use the Service</li>
              <li>Any conduct or content of any third party on the Service</li>
              <li>Any unauthorized access, use or alteration of your transmissions or content</li>
            </ul>
          </div>
          
          <div className="tos-section">
            <h2>9. Governing Law</h2>
            <p>
              These Terms shall be governed and construed in accordance with the laws of the jurisdiction where Exam Portal operates, without 
              regard to its conflict of law provisions.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>10. Changes to Terms</h2>
            <p>
              We reserve the right, at our sole discretion, to modify or replace these Terms at any time. We will provide notice of any 
              changes by posting the new Terms on this page. Your continued use of the Service after any such changes constitutes your 
              acceptance of the new Terms.
            </p>
          </div>
          
          <div className="tos-section">
            <h2>11. Contact Information</h2>
            <p>
              If you have any questions about these Terms, please contact <NAME_EMAIL>.
            </p>
          </div>
          
          <div className="action-buttons">
            <button 
              className="back-button"
              onClick={() => navigate(-1)}
            >
              Back
            </button>
            <button 
              className="agree-button"
              onClick={() => navigate('/signup')}
            >
              I Agree
            </button>
          </div>
        </div>
      </div>

      <style>{`
        .tos-container {
          min-height: 100vh;
          background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1rem;
        }

        .tos-card {
          width: 100%;
          max-width: 50rem;
          background: white;
          border-radius: 0.75rem;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          max-height: 90vh;
          display: flex;
          flex-direction: column;
        }

        .tos-header {
          background: #4f46e5;
          padding: 1.5rem 2rem;
          text-align: center;
          color: white;
          position: sticky;
          top: 0;
          z-index: 10;
        }

        .tos-header h1 {
          font-size: 1.5rem;
          font-weight: 700;
          margin: 0;
        }

        .tos-header p {
          color: #a5b4fc;
          margin-top: 0.25rem;
          font-size: 0.875rem;
        }

        .tos-content {
          padding: 2rem;
          overflow-y: auto;
          flex-grow: 1;
        }

        .tos-section {
          margin-bottom: 1.5rem;
        }

        .tos-section h2 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0 0 0.75rem 0;
        }

        .tos-section p {
          font-size: 0.875rem;
          color: #4b5563;
          line-height: 1.5;
          margin: 0 0 0.75rem 0;
        }

        .tos-section ul {
          margin: 0.75rem 0;
          padding-left: 1.25rem;
        }

        .tos-section li {
          font-size: 0.875rem;
          color: #4b5563;
          line-height: 1.5;
          margin-bottom: 0.25rem;
        }

        .action-buttons {
          display: flex;
          justify-content: space-between;
          margin-top: 2rem;
          gap: 1rem;
        }

        .back-button {
          padding: 0.5rem 1rem;
          background: white;
          color: #4f46e5;
          border: 1px solid #4f46e5;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: all 0.2s;
          flex: 1;
        }

        .back-button:hover {
          background: #f5f7ff;
        }

        .agree-button {
          padding: 0.5rem 1rem;
          background: #4f46e5;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          font-size: 0.875rem;
          cursor: pointer;
          transition: background 0.2s;
          flex: 1;
        }

        .agree-button:hover {
          background: #4338ca;
        }

        @media (max-width: 640px) {
          .tos-card {
            max-height: 85vh;
          }
          
          .tos-content {
            padding: 1.5rem;
          }
          
          .action-buttons {
            flex-direction: column;
          }
        }
      `}</style>
    </div>
  );
};

export default TermsOfService;