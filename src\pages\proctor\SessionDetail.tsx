import { useParams, useNavigate } from 'react-router-dom';
import { 
  AlertCircle, 
  MessageSquare, 
  Flag, 
  Video, 
  Users, 
  Clock, 
  ChevronLeft,
  Maximize,
  Settings,
  Mic,
  MicOff,
  VideoOff,
  User,
  MoreVertical
} from 'lucide-react';
import { useState, useRef, useEffect } from 'react';

const SessionDetail = () => {
  const { sessionId } = useParams();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [activeTab, setActiveTab] = useState('flags');
  const [chatMessage, setChatMessage] = useState('');
  const [chatMessages, setChatMessages] = useState([
    { id: 1, sender: 'Proctor', message: 'Please keep your face visible', time: '10:02 AM' },
    { id: 2, sender: 'Student', message: 'Understood, adjusting camera', time: '10:03 AM' }
  ]);
  const [flags, setFlags] = useState([
    { id: 1, time: '00:10', type: 'No face detected', severity: 'medium', resolved: false },
    { id: 2, time: '00:20', type: 'Multiple people', severity: 'high', resolved: true },
    { id: 3, time: '00:45', type: 'Phone detected', severity: 'high', resolved: false },
    { id: 4, time: '01:15', type: 'Background noise', severity: 'low', resolved: false }
  ]);
  const [sessionTime, setSessionTime] = useState('00:00:00');
  const [participants, setParticipants] = useState([
    { id: 1, name: 'John Doe', video: true, audio: true, active: true },
    { id: 2, name: 'Jane Smith', video: false, audio: true, active: true },
    { id: 3, name: 'Alex Johnson', video: true, audio: false, active: false }
  ]);

  // Simulate live video (in a real app, this would be a real stream)
  useEffect(() => {
    const timer = setInterval(() => {
      const date = new Date(0);
      date.setSeconds(parseInt(sessionTime.split(':')[2]) + 1);
      setSessionTime(date.toISOString().substr(11, 8));
    }, 1000);

    return () => clearInterval(timer);
  }, [sessionTime]);

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      videoRef.current?.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      const newMessage = {
        id: chatMessages.length + 1,
        sender: 'Proctor',
        message: chatMessage,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };
      setChatMessages([...chatMessages, newMessage]);
      setChatMessage('');
    }
  };

  const toggleFlagResolution = (flagId: number) => {
    setFlags(flags.map(flag => 
      flag.id === flagId ? { ...flag, resolved: !flag.resolved } : flag
    ));
  };

  return (
    <div className="session-detail-page">
      <div className="session-header">
        <button 
          onClick={() => navigate(-1)}
          className="back-button"
        >
          <ChevronLeft size={20} />
          Back to Dashboard
        </button>
        <h1 className="session-title">
          <Video size={24} className="header-icon" />
          Session: {sessionId}
        </h1>
        <div className="session-time">
          <Clock size={16} />
          <span>{sessionTime}</span>
        </div>
      </div>

      <div className="session-layout">
        {/* Main Content Area */}
        <div className="main-content">
          {/* Video Section */}
          <div className="video-container">
            <div className="video-header">
              <h2 className="video-title">
                <Video size={18} className="mr-1" />
                Live Proctoring Feed
              </h2>
              <div className="video-controls">
                <button 
                  onClick={() => setIsMuted(!isMuted)}
                  className={`control-button ${isMuted ? 'active' : ''}`}
                  title={isMuted ? 'Unmute' : 'Mute'}
                >
                  {isMuted ? <MicOff size={16} /> : <Mic size={16} />}
                </button>
                <button 
                  onClick={() => setIsVideoOff(!isVideoOff)}
                  className={`control-button ${isVideoOff ? 'active' : ''}`}
                  title={isVideoOff ? 'Show Video' : 'Hide Video'}
                >
                  {isVideoOff ? <VideoOff size={16} /> : <Video size={16} />}
                </button>
                <button 
                  onClick={toggleFullscreen}
                  className="control-button"
                  title="Fullscreen"
                >
                  <Maximize size={16} />
                </button>
                <button className="control-button" title="Settings">
                  <Settings size={16} />
                </button>
              </div>
            </div>
            <div className="video-wrapper">
              <video 
                ref={videoRef}
                className="live-video"
                autoPlay
                muted={isMuted}
                controls={false}
                poster="https://via.placeholder.com/800x450?text=Live+Proctoring+Feed"
              >
                {/* In a real app, this would be your video stream source */}
                <source src="" type="video/mp4" />
              </video>
              <div className="video-overlay">
                <div className="recording-indicator">
                  <div className="recording-dot"></div>
                  <span>REC</span>
                </div>
                <div className="current-time">{sessionTime}</div>
              </div>
            </div>
          </div>

          {/* Participants Grid */}
          <div className="participants-section">
            <h3 className="section-title">
              <Users size={18} className="mr-1" />
              Participants ({participants.length})
            </h3>
            <div className="participants-grid">
              {participants.map(participant => (
                <div 
                  key={participant.id} 
                  className={`participant-card ${participant.active ? '' : 'inactive'}`}
                >
                  <div className="participant-video">
                    {participant.video ? (
                      <div className="video-placeholder">
                        <User size={24} />
                      </div>
                    ) : (
                      <div className="video-off">
                        <VideoOff size={24} />
                      </div>
                    )}
                    <div className="participant-status">
                      {participant.audio ? (
                        <Mic size={12} />
                      ) : (
                        <MicOff size={12} />
                      )}
                    </div>
                  </div>
                  <div className="participant-info">
                    <span className="participant-name">{participant.name}</span>
                    <button className="participant-menu" title="Participant options">
                      <MoreVertical size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="sidebar">
          <div className="sidebar-tabs">
            <button 
              className={`tab-button ${activeTab === 'flags' ? 'active' : ''}`}
              onClick={() => setActiveTab('flags')}
            >
              <Flag size={16} className="mr-1" />
              Flags ({flags.filter(f => !f.resolved).length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'chat' ? 'active' : ''}`}
              onClick={() => setActiveTab('chat')}
            >
              <MessageSquare size={16} className="mr-1" />
              Chat
            </button>
          </div>

          {activeTab === 'flags' ? (
            <div className="flags-container">
              <div className="flags-header">
                <h3 className="flags-title">Flag Timeline</h3>
                <button className="resolve-all">Mark All Resolved</button>
              </div>
              <ul className="flags-list">
                {flags.map(flag => (
                  <li key={flag.id} className={`flag-item ${flag.severity} ${flag.resolved ? 'resolved' : ''}`}>
                    <div className="flag-time">{flag.time}</div>
                    <div className="flag-content">
                      <div className="flag-type">
                        <AlertCircle size={14} className="mr-1" />
                        {flag.type}
                      </div>
                      <button 
                        className={`flag-resolve ${flag.resolved ? 'resolved' : ''}`}
                        onClick={() => toggleFlagResolution(flag.id)}
                      >
                        {flag.resolved ? 'Resolved' : 'Mark Resolved'}
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <div className="chat-container">
              <div className="chat-messages">
                {chatMessages.map(msg => (
                  <div key={msg.id} className={`chat-message ${msg.sender === 'Proctor' ? 'sent' : 'received'}`}>
                    <div className="message-header">
                      <span className="message-sender">{msg.sender}</span>
                      <span className="message-time">{msg.time}</span>
                    </div>
                    <div className="message-content">{msg.message}</div>
                  </div>
                ))}
              </div>
              <div className="chat-input">
                <input
                  type="text"
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Type a message..."
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                />
                <button 
                  onClick={handleSendMessage}
                  disabled={!chatMessage.trim()}
                  className="send-button"
                >
                  Send
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <style>{`
        .session-detail-page {
          padding: 1.5rem;
          max-width: 1800px;
          margin: 0 auto;
          height: 100vh;
          display: flex;
          flex-direction: column;
        }

        .session-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 1.5rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .back-button {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          background-color: #f3f4f6;
          color: #4b5563;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .back-button:hover {
          background-color: #e5e7eb;
        }

        .session-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          font-size: 1.5rem;
          font-weight: 700;
          color: #15803d;
          margin: 0;
        }

        .header-icon {
          color: #15803d;
        }

        .session-time {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 500;
          color: #4b5563;
          background-color: #f3f4f6;
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
        }

        .session-layout {
          display: grid;
          grid-template-columns: 1fr 350px;
          gap: 1.5rem;
          height: calc(100vh - 100px);
        }

        .main-content {
          display: flex;
          flex-direction: column;
          gap: 1.5rem;
        }

        .video-container {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .video-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          background-color: #f8fafc;
          border-bottom: 1px solid #e5e7eb;
        }

        .video-title {
          display: flex;
          align-items: center;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin: 0;
        }

        .video-controls {
          display: flex;
          gap: 0.5rem;
        }

        .control-button {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 2.5rem;
          height: 2.5rem;
          border-radius: 0.375rem;
          background-color: white;
          border: 1px solid #d1d5db;
          color: #4b5563;
          cursor: pointer;
          transition: all 0.2s;
        }

        .control-button:hover {
          background-color: #f3f4f6;
        }

        .control-button.active {
          background-color: #e5e7eb;
        }

        .video-wrapper {
          position: relative;
          width: 100%;
          aspect-ratio: 16/9;
          background-color: #000;
        }

        .live-video {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .video-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          pointer-events: none;
        }

        .recording-indicator {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          background-color: rgba(239, 68, 68, 0.8);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .recording-dot {
          width: 0.75rem;
          height: 0.75rem;
          background-color: white;
          border-radius: 50%;
          animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }

        .current-time {
          background-color: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .participants-section {
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          padding: 1.5rem;
        }

        .section-title {
          display: flex;
          align-items: center;
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 1rem;
        }

        .participants-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 1rem;
        }

        .participant-card {
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
          overflow: hidden;
          transition: transform 0.2s;
        }

        .participant-card:hover {
          transform: translateY(-2px);
        }

        .participant-card.inactive {
          opacity: 0.6;
        }

        .participant-video {
          position: relative;
          width: 100%;
          aspect-ratio: 16/9;
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .video-placeholder {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background-color: #e5e7eb;
          color: #9ca3af;
        }

        .video-off {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background-color: #f3f4f6;
          color: #9ca3af;
        }

        .participant-status {
          position: absolute;
          bottom: 0.5rem;
          right: 0.5rem;
          width: 1.5rem;
          height: 1.5rem;
          background-color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .participant-info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.75rem;
          background-color: white;
        }

        .participant-name {
          font-weight: 500;
          font-size: 0.875rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .participant-menu {
          background: none;
          border: none;
          color: #9ca3af;
          cursor: pointer;
          padding: 0.25rem;
        }

        /* Sidebar */
        .sidebar {
          display: flex;
          flex-direction: column;
          background-color: white;
          border-radius: 0.75rem;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .sidebar-tabs {
          display: flex;
          border-bottom: 1px solid #e5e7eb;
        }

        .tab-button {
          flex: 1;
          padding: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          border: none;
          cursor: pointer;
          font-weight: 500;
          color: #64748b;
          transition: all 0.2s;
        }

        .tab-button.active {
          color: #15803d;
          border-bottom: 2px solid #15803d;
          background-color: #f0fdf4;
        }

        .tab-button:hover:not(.active) {
          background-color: #f8fafc;
        }

        /* Flags Container */
        .flags-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .flags-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 1.5rem;
          border-bottom: 1px solid #e5e7eb;
        }

        .flags-title {
          font-weight: 600;
          margin: 0;
        }

        .resolve-all {
          background: none;
          border: none;
          color: #15803d;
          font-weight: 500;
          cursor: pointer;
          font-size: 0.875rem;
        }

        .flags-list {
          flex: 1;
          overflow-y: auto;
          padding: 0;
          margin: 0;
          list-style: none;
        }

        .flag-item {
          padding: 1rem 1.5rem;
          border-bottom: 1px solid #e5e7eb;
          transition: background-color 0.2s;
        }

        .flag-item:hover {
          background-color: #f8fafc;
        }

        .flag-item.high {
          border-left: 3px solid #ef4444;
        }

        .flag-item.medium {
          border-left: 3px solid #f59e0b;
        }

        .flag-item.low {
          border-left: 3px solid #3b82f6;
        }

        .flag-item.resolved {
          opacity: 0.7;
          background-color: #f8fafc;
        }

        .flag-time {
          font-size: 0.75rem;
          color: #64748b;
          margin-bottom: 0.25rem;
        }

        .flag-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .flag-type {
          display: flex;
          align-items: center;
          font-weight: 500;
        }

        .flag-resolve {
          padding: 0.25rem 0.75rem;
          font-size: 0.75rem;
          border-radius: 9999px;
          background-color: #e5e7eb;
          border: none;
          cursor: pointer;
          transition: all 0.2s;
        }

        .flag-resolve:hover {
          background-color: #d1d5db;
        }

        .flag-resolve.resolved {
          background-color: #dcfce7;
          color: #166534;
        }

        /* Chat Container */
        .chat-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 1rem 1.5rem;
        }

        .chat-message {
          margin-bottom: 1rem;
          max-width: 80%;
        }

        .chat-message.sent {
          margin-left: auto;
        }

        .chat-message.received {
          margin-right: auto;
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 0.25rem;
        }

        .message-sender {
          font-weight: 600;
          font-size: 0.875rem;
        }

        .message-sender.sent {
          color: #15803d;
        }

        .message-sender.received {
          color: #3b82f6;
        }

        .message-time {
          font-size: 0.75rem;
          color: #64748b;
        }

        .message-content {
          padding: 0.75rem;
          border-radius: 0.75rem;
          font-size: 0.875rem;
        }

        .sent .message-content {
          background-color: #dcfce7;
          color: #166534;
          border-top-right-radius: 0;
        }

        .received .message-content {
          background-color: #eff6ff;
          color: #1e40af;
          border-top-left-radius: 0;
        }

        .chat-input {
          display: flex;
          padding: 1rem 1.5rem;
          border-top: 1px solid #e5e7eb;
        }

        .chat-input input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          margin-right: 0.5rem;
        }

        .chat-input input:focus {
          outline: none;
          border-color: #86efac;
          box-shadow: 0 0 0 2px rgba(74, 222, 128, 0.2);
        }

        .send-button {
          padding: 0 1.5rem;
          background-color: #15803d;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .send-button:hover {
          background-color: #166534;
        }

        .send-button:disabled {
          background-color: #d1d5db;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};

export default SessionDetail;